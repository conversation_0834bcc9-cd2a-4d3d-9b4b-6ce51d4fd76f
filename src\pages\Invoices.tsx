"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Search, Download, Eye, CalendarIcon, FileText, TrendingUp, Activity, DollarSign, CheckCircle, Clock, AlertTriangle, Filter, Grid, List } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { InvoiceDetailsModal } from "@/components/modals/InvoiceDetailsModal";
import { PDFPreviewModal } from "@/components/modals/PDFPreviewModal";
import { useToast } from "@/hooks/use-toast";

interface Invoice {
  id: string;
  number: string;
  date: string;
  service: string;
  total: number;
  paid: number;
  remaining: number;
  status: "paid" | "partial" | "pending" | "overdue";
}

const mockInvoices: Invoice[] = [
  {
    id: "1",
    number: "INV-2024-001",
    date: "2024-01-15",
    service: "تغيير زيت المحرك",
    total: 300,
    paid: 300,
    remaining: 0,
    status: "paid"
  },
  {
    id: "2",
    number: "INV-2024-002",
    date: "2024-01-20",
    service: "إصلاح الفرامل",
    total: 850,
    paid: 500,
    remaining: 350,
    status: "partial"
  },
  {
    id: "3",
    number: "INV-2024-003",
    date: "2024-02-05",
    service: "صيانة شاملة",
    total: 1200,
    paid: 0,
    remaining: 1200,
    status: "pending"
  },
  {
    id: "4",
    number: "INV-2024-004",
    date: "2024-02-10",
    service: "تغيير الإطارات",
    total: 600,
    paid: 0,
    remaining: 600,
    status: "overdue"
  }
];

const getStatusBadge = (status: Invoice["status"]) => {
  const statusConfig = {
    paid: { label: "مدفوع", variant: "default" as const, className: "bg-success text-success-foreground" },
    partial: { label: "مدفوع جزئياً", variant: "secondary" as const, className: "bg-warning text-warning-foreground" },
    pending: { label: "معلق", variant: "outline" as const, className: "border-muted-foreground text-muted-foreground" },
    overdue: { label: "متأخر", variant: "destructive" as const, className: "bg-destructive text-destructive-foreground" }
  };
  
  const config = statusConfig[status];
  return (
    <Badge variant={config.variant} className={config.className}>
      {config.label}
    </Badge>
  );
};

export default function Invoices() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateFrom, setDateFrom] = useState<Date>();
  const [dateTo, setDateTo] = useState<Date>();
  const [sortField, setSortField] = useState<string>("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const { toast } = useToast();

  const totalPayments = mockInvoices.reduce((sum, inv) => sum + inv.paid, 0);
  const outstandingBalance = mockInvoices.reduce((sum, inv) => sum + inv.remaining, 0);
  const totalInvoices = mockInvoices.length;
  const lastPayment = Math.max(...mockInvoices.map(inv => inv.paid));

  const filteredInvoices = mockInvoices
    .filter(invoice => {
      const matchesSearch = invoice.service.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           invoice.number.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === "all" || invoice.status === statusFilter;
      const invoiceDate = new Date(invoice.date);
      const matchesDateRange = (!dateFrom || invoiceDate >= dateFrom) && 
                              (!dateTo || invoiceDate <= dateTo);
      
      return matchesSearch && matchesStatus && matchesDateRange;
    })
    .sort((a, b) => {
      const aValue = a[sortField as keyof Invoice];
      const bValue = b[sortField as keyof Invoice];
      
      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/5" dir="rtl">
      {/* Mobile App Header */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-primary/5 to-accent/10 rounded-b-[2rem] md:rounded-b-[3rem] -z-10" />
        <div className="px-4 pt-6 pb-8 md:p-8">
          {/* Mobile Header */}
          <div className="flex items-center justify-between mb-6 md:hidden">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-primary via-primary-light to-primary-dark flex items-center justify-center shadow-xl">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-almarai font-bold text-foreground">
                  الفواتير
                </h1>
                <p className="text-sm text-muted-foreground font-arabic">
                  {filteredInvoices.length} فاتورة
                </p>
              </div>
            </div>
          </div>

          {/* Desktop Header */}
          <div className="hidden md:flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-6">
                <div className="w-20 h-20 rounded-3xl bg-gradient-to-br from-primary via-primary-light to-primary-dark flex items-center justify-center shadow-2xl">
                  <FileText className="w-10 h-10 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl lg:text-5xl font-almarai font-bold bg-gradient-to-r from-foreground via-primary to-primary-dark bg-clip-text text-transparent">
                    الفواتير والدفعات
                  </h1>
                  <p className="text-lg text-muted-foreground font-arabic mt-2">
                    إدارة جميع فواتير الخدمات والمدفوعات
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 md:px-8 -mt-4 md:-mt-8 relative z-10 space-y-6 md:space-y-8">
        {/* Mobile Stats Cards */}
        <div className="grid grid-cols-2 gap-3 md:hidden">
          <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-emerald-500/10 via-teal-500/5 to-cyan-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 via-transparent to-teal-500/20 opacity-50" />
            <CardContent className="relative p-3 text-center">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-lg mx-auto mb-2">
                <DollarSign className="w-4 h-4 text-white" />
              </div>
              <div className="text-lg font-bold text-foreground font-almarai">
                {(totalPayments/1000).toFixed(1)}k
              </div>
              <div className="text-xs text-muted-foreground font-arabic">إجمالي الدفعات</div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-indigo-500/10 via-purple-500/5 to-pink-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-transparent to-purple-500/20 opacity-50" />
            <CardContent className="relative p-3 text-center">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg mx-auto mb-2">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <div className="text-lg font-bold text-foreground font-almarai">
                {totalInvoices}
              </div>
              <div className="text-xs text-muted-foreground font-arabic">إجمالي الفواتير</div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop Summary Cards */}
        <div className="hidden md:grid grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-emerald-500/10 via-teal-500/5 to-cyan-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 via-transparent to-teal-500/20 opacity-50" />
            <CardHeader className="relative flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-arabic text-muted-foreground">
                إجمالي الدفعات
              </CardTitle>
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-xl">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
            </CardHeader>
            <CardContent className="relative">
              <div className="text-3xl font-bold text-foreground font-almarai">{totalPayments.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground font-arabic">ريال سعودي</p>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-500/20 via-transparent to-orange-500/20 opacity-50" />
            <CardHeader className="relative flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-arabic text-muted-foreground">
                الرصيد المتبقي
              </CardTitle>
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center shadow-xl">
                <AlertTriangle className="w-6 h-6 text-white" />
              </div>
            </CardHeader>
            <CardContent className="relative">
              <div className="text-3xl font-bold text-foreground font-almarai">{outstandingBalance.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground font-arabic">ريال سعودي</p>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-indigo-500/10 via-purple-500/5 to-pink-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-transparent to-purple-500/20 opacity-50" />
            <CardHeader className="relative flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-arabic text-muted-foreground">
                عدد الفواتير
              </CardTitle>
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-xl">
                <FileText className="w-6 h-6 text-white" />
              </div>
            </CardHeader>
            <CardContent className="relative">
              <div className="text-3xl font-bold text-foreground font-almarai">{totalInvoices}</div>
              <p className="text-xs text-muted-foreground font-arabic">فاتورة</p>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-violet-500/10 via-purple-500/5 to-fuchsia-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 via-transparent to-purple-500/20 opacity-50" />
            <CardHeader className="relative flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-arabic text-muted-foreground">
                آخر دفعة
              </CardTitle>
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center shadow-xl">
                <CheckCircle className="w-6 h-6 text-white" />
              </div>
            </CardHeader>
            <CardContent className="relative">
              <div className="text-3xl font-bold text-foreground font-almarai">{lastPayment.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground font-arabic">ريال سعودي</p>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Filters and Search */}
        <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-50" />
          <CardContent className="relative p-4 md:p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="البحث في الفواتير..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 font-arabic bg-background/50 backdrop-blur-sm border-border/50 focus:border-primary/50 rounded-xl h-12"
                />
              </div>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full lg:w-[200px] font-arabic">
                <SelectValue placeholder="حالة الدفع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="paid">مدفوع</SelectItem>
                <SelectItem value="partial">مدفوع جزئياً</SelectItem>
                <SelectItem value="pending">معلق</SelectItem>
                <SelectItem value="overdue">متأخر</SelectItem>
              </SelectContent>
            </Select>

            {/* Date Range */}
            <div className="flex gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full lg:w-[140px] justify-start text-left font-normal font-arabic",
                      !dateFrom && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateFrom ? format(dateFrom, "yyyy/MM/dd") : "من تاريخ"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dateFrom}
                    onSelect={setDateFrom}
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full lg:w-[140px] justify-start text-left font-normal font-arabic",
                      !dateTo && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateTo ? format(dateTo, "yyyy/MM/dd") : "إلى تاريخ"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dateTo}
                    onSelect={setDateTo}
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoices Table */}
      <Card className="premium-card">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="font-almarai text-xl">قائمة الفواتير</CardTitle>
            <div className="flex gap-2">
              <PDFPreviewModal invoices={filteredInvoices} title="تقرير الفواتير والمدفوعات">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="font-arabic"
                >
                  <Download className="w-4 h-4 mr-2" />
                  تصدير PDF
                </Button>
              </PDFPreviewModal>
              <Select value={`${sortField}-${sortOrder}`} onValueChange={(value) => {
                const [field, order] = value.split('-');
                setSortField(field);
                setSortOrder(order as "asc" | "desc");
              }}>
                <SelectTrigger className="w-[150px] font-arabic">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date-desc">الأحدث أولاً</SelectItem>
                  <SelectItem value="date-asc">الأقدم أولاً</SelectItem>
                  <SelectItem value="total-desc">المبلغ (عالي)</SelectItem>
                  <SelectItem value="total-asc">المبلغ (منخفض)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-border/50">
                  <TableHead className="text-right font-arabic font-semibold">رقم الفاتورة</TableHead>
                  <TableHead className="text-right font-arabic font-semibold">التاريخ</TableHead>
                  <TableHead className="text-right font-arabic font-semibold">الخدمة</TableHead>
                  <TableHead className="text-right font-arabic font-semibold">الإجمالي</TableHead>
                  <TableHead className="text-right font-arabic font-semibold">المدفوع</TableHead>
                  <TableHead className="text-right font-arabic font-semibold">المتبقي</TableHead>
                  <TableHead className="text-right font-arabic font-semibold">الحالة</TableHead>
                  <TableHead className="text-right font-arabic font-semibold">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInvoices.map((invoice) => (
                  <TableRow key={invoice.id} className="hover:bg-muted/50 border-border/30">
                    <TableCell className="font-mono text-sm">{invoice.number}</TableCell>
                    <TableCell className="font-arabic">{new Date(invoice.date).toLocaleDateString('ar-SA')}</TableCell>
                    <TableCell className="font-arabic font-medium">{invoice.service}</TableCell>
                    <TableCell className="font-mono">{invoice.total.toLocaleString()} ر.س</TableCell>
                    <TableCell className="font-mono text-success">{invoice.paid.toLocaleString()} ر.س</TableCell>
                    <TableCell className="font-mono text-warning">{invoice.remaining.toLocaleString()} ر.س</TableCell>
                    <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <InvoiceDetailsModal invoice={invoice}>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        </InvoiceDetailsModal>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => toast({
                            title: "تحميل الفاتورة",
                            description: `جاري تحميل فاتورة ${invoice.number}`,
                          })}
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          
          {filteredInvoices.length === 0 && (
            <div className="text-center py-12">
              <p className="text-muted-foreground font-arabic">لا توجد فواتير تطابق معايير البحث</p>
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </div>
  );
}