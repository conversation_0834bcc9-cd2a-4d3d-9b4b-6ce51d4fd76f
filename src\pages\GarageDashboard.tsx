import { StatsCard } from "@/components/dashboard/StatsCard";
import { OrdersTable } from "@/components/garage/OrdersTable";
import { StatisticsCharts } from "@/components/garage/StatisticsCharts";
import { Package, Settings, Clock, DollarSign } from "lucide-react";

const GarageDashboard = () => {
  const stats = [
    {
      title: "الطلبات الجديدة",
      value: "15",
      icon: Package,
      change: "+3 اليوم",
      changeType: "positive" as const,
      gradient: "blue" as const
    },
    {
      title: "قيد الإصلاح",
      value: "25",
      icon: Settings,
      change: "+5 هذا الأسبوع",
      changeType: "positive" as const,
      gradient: "orange" as const
    },
    {
      title: "جاهز للاستلام",
      value: "18",
      icon: Clock,
      change: "+7 اليوم",
      changeType: "positive" as const,
      gradient: "green" as const
    },
    {
      title: "إجمالي الأرباح",
      value: "45,230 ريال",
      icon: DollarSign,
      change: "+22% هذا الشهر",
      changeType: "positive" as const,
      gradient: "purple" as const
    }
  ];

  return (
    <div className="flex-1 space-y-6 p-6" dir="rtl">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-foreground font-arabic">
          لوحة تحكم الكراج
        </h1>
        <p className="text-muted-foreground font-arabic">
          مرحباً بك في نظام إدارة الكراج الخاص بك
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            change={stat.change}
            changeType={stat.changeType}
            gradient={stat.gradient}
          />
        ))}
      </div>

      {/* Statistics Section */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-foreground font-arabic">الإحصائيات</h2>
        <StatisticsCharts />
      </div>

      {/* Recent Orders Table */}
      <div className="space-y-6">
        <OrdersTable />
      </div>
    </div>
  );
};

export default GarageDashboard;