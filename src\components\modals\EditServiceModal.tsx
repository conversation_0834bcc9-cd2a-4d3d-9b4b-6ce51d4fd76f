import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Save, Upload, X, Star } from "lucide-react";

interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: string;
  isActive: boolean;
  ordersCount: number;
  rating: number;
  hasImage: boolean;
}

interface EditServiceModalProps {
  service: Service | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (service: Service) => void;
}

export const EditServiceModal = ({ service, isOpen, onClose, onUpdate }: EditServiceModalProps) => {
  const [serviceName, setServiceName] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState("");
  const [duration, setDuration] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [hasImage, setHasImage] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (service) {
      setServiceName(service.name);
      setDescription(service.description);
      setPrice(service.price.toString());
      setDuration(service.duration);
      setIsActive(service.isActive);
      setHasImage(service.hasImage);
    }
  }, [service]);

  const handleSubmit = () => {
    if (!service || !serviceName || !description || !price || !duration) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    const updatedService = {
      ...service,
      name: serviceName,
      description,
      price: Number(price),
      duration,
      isActive,
      hasImage
    };

    onUpdate(updatedService);
    toast({
      title: "تم تحديث الخدمة",
      description: "تم تحديث بيانات الخدمة بنجاح",
    });
    onClose();
  };

  const handleImageUpload = () => {
    setHasImage(true);
    toast({
      title: "تم رفع الصورة",
      description: "تم رفع صورة الخدمة بنجاح",
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < Math.floor(rating)
            ? 'fill-yellow-400 text-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  if (!service) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl" dir="rtl">
        <DialogHeader>
          <DialogTitle className="font-arabic">تعديل الخدمة</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Service Stats */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">{service.ordersCount}</p>
              <p className="text-sm text-muted-foreground font-arabic">طلب</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1">
                {renderStars(service.rating)}
              </div>
              <p className="text-sm text-muted-foreground font-arabic">{service.rating} تقييم</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{service.price}</p>
              <p className="text-sm text-muted-foreground font-arabic">ريال</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="serviceName" className="font-arabic">اسم الخدمة *</Label>
              <Input
                id="serviceName"
                value={serviceName}
                onChange={(e) => setServiceName(e.target.value)}
                className="font-arabic"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="duration" className="font-arabic">المدة المتوقعة *</Label>
              <Input
                id="duration"
                value={duration}
                onChange={(e) => setDuration(e.target.value)}
                className="font-arabic"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="font-arabic">وصف الخدمة *</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="font-arabic"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="price" className="font-arabic">السعر (ريال) *</Label>
            <Input
              id="price"
              type="number"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              className="font-mono"
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="isActive" className="font-arabic">تفعيل الخدمة</Label>
              <Switch
                id="isActive"
                checked={isActive}
                onCheckedChange={setIsActive}
              />
            </div>

            <div className="space-y-3">
              <Label className="font-arabic">صورة الخدمة</Label>
              <div className="flex items-center gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleImageUpload}
                  className="font-arabic"
                >
                  <Upload className="h-4 w-4 ml-2" />
                  {hasImage ? 'تغيير الصورة' : 'رفع صورة'}
                </Button>
                {hasImage && (
                  <div className="flex items-center gap-2 text-green-600">
                    <span className="text-sm font-arabic">متوفر</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setHasImage(false)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button onClick={handleSubmit} className="font-arabic">
              <Save className="h-4 w-4 ml-2" />
              حفظ التغييرات
            </Button>
            <Button variant="outline" onClick={onClose} className="font-arabic">
              إلغاء
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};