import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Edit, Trash2, Plus, Search, Image, Star, Eye, EyeOff } from "lucide-react";
import { AddServiceModal } from "@/components/modals/AddServiceModal";
import { EditServiceModal } from "@/components/modals/EditServiceModal";
import { DeleteConfirmModal } from "@/components/modals/DeleteConfirmModal";
import { useToast } from "@/hooks/use-toast";

interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: string;
  isActive: boolean;
  ordersCount: number;
  rating: number;
  hasImage: boolean;
}

const mockServices: Service[] = [
  {
    id: '1',
    name: 'تغيير زيت المحرك',
    description: 'تغيير زيت المحرك وفلتر الزيت مع فحص شامل للمحرك',
    price: 150,
    duration: '30 دقيقة',
    isActive: true,
    ordersCount: 45,
    rating: 4.8,
    hasImage: true
  },
  {
    id: '2',
    name: 'فحص شامل للسيارة',
    description: 'فحص جميع أجزاء السيارة والتأكد من سلامتها',
    price: 200,
    duration: '60 دقيقة',
    isActive: true,
    ordersCount: 32,
    rating: 4.9,
    hasImage: true
  },
  {
    id: '3',
    name: 'إصلاح المكابح',
    description: 'فحص وإصلاح منظومة المكابح وتبديل الأقراص إذا لزم الأمر',
    price: 400,
    duration: '90 دقيقة',
    isActive: true,
    ordersCount: 28,
    rating: 4.7,
    hasImage: false
  },
  {
    id: '4',
    name: 'تبديل الإطارات',
    description: 'تبديل الإطارات وفحص ضغط الهواء والتوازن',
    price: 80,
    duration: '20 دقيقة',
    isActive: false,
    ordersCount: 15,
    rating: 4.5,
    hasImage: true
  },
  {
    id: '5',
    name: 'إصلاح التكييف',
    description: 'فحص وإصلاح نظام التكييف وتعبئة الغاز',
    price: 300,
    duration: '120 دقيقة',
    isActive: true,
    ordersCount: 22,
    rating: 4.6,
    hasImage: false
  }
];

const Services = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [services, setServices] = useState(mockServices);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const { toast } = useToast();

  const toggleServiceStatus = (serviceId: string) => {
    setServices(services.map(service => 
      service.id === serviceId 
        ? { ...service, isActive: !service.isActive }
        : service
    ));
    toast({
      title: "تم تحديث حالة الخدمة",
      description: "تم تغيير حالة تفعيل الخدمة بنجاح",
    });
  };

  const filteredServices = services.filter(service => 
    service.name.includes(searchTerm) || 
    service.description.includes(searchTerm)
  );

  const handleAddService = (newService: Service) => {
    setServices([...services, newService]);
  };

  const handleUpdateService = (updatedService: Service) => {
    setServices(services.map(service => 
      service.id === updatedService.id ? updatedService : service
    ));
  };

  const handleDeleteService = (serviceId: string) => {
    setServices(services.filter(service => service.id !== serviceId));
    toast({
      title: "تم حذف الخدمة",
      description: "تم حذف الخدمة بنجاح",
    });
  };

  const handleEditClick = (service: Service) => {
    setSelectedService(service);
    setIsEditModalOpen(true);
  };

  const handleDeleteClick = (service: Service) => {
    setSelectedService(service);
    setIsDeleteModalOpen(true);
  };

  const handleImageUpload = (service: Service) => {
    // Simulate image upload
    setServices(services.map(s => 
      s.id === service.id ? { ...s, hasImage: true } : s
    ));
    toast({
      title: "تم رفع الصورة",
      description: `تم رفع صورة للخدمة ${service.name}`,
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < Math.floor(rating)
            ? 'fill-yellow-400 text-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="flex-1 space-y-6 p-6" dir="rtl">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight text-foreground font-arabic">
            إدارة الخدمات
          </h1>
          <p className="text-muted-foreground font-arabic">
            إدارة خدمات الكراج والأسعار والتقييمات
          </p>
        </div>
        <Button 
          onClick={() => setIsAddModalOpen(true)}
          className="bg-primary hover:bg-primary/90 font-arabic"
        >
          <Plus className="h-4 w-4 ml-2" />
          إضافة خدمة جديدة
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-foreground">{services.length}</p>
              <p className="text-sm text-muted-foreground font-arabic">إجمالي الخدمات</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{services.filter(s => s.isActive).length}</p>
              <p className="text-sm text-muted-foreground font-arabic">خدمات نشطة</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {services.reduce((acc, service) => acc + service.ordersCount, 0)}
              </p>
              <p className="text-sm text-muted-foreground font-arabic">إجمالي الطلبات</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-foreground">
                {(services.reduce((acc, service) => acc + service.rating, 0) / services.length).toFixed(1)}
              </p>
              <p className="text-sm text-muted-foreground font-arabic">متوسط التقييم</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="البحث عن خدمة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10 font-arabic"
            />
          </div>
        </CardContent>
      </Card>

      {/* Services Table */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">قائمة الخدمات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right font-arabic">اسم الخدمة</TableHead>
                  <TableHead className="text-right font-arabic">الوصف</TableHead>
                  <TableHead className="text-right font-arabic">السعر</TableHead>
                  <TableHead className="text-right font-arabic">المدة المتوقعة</TableHead>
                  <TableHead className="text-right font-arabic">عدد الطلبات</TableHead>
                  <TableHead className="text-right font-arabic">التقييم</TableHead>
                  <TableHead className="text-right font-arabic">الصورة</TableHead>
                  <TableHead className="text-right font-arabic">الحالة</TableHead>
                  <TableHead className="text-right font-arabic">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredServices.map((service) => (
                  <TableRow key={service.id} className="hover:bg-muted/50">
                    <TableCell className="font-arabic font-medium">{service.name}</TableCell>
                    <TableCell className="font-arabic text-sm text-muted-foreground max-w-64 truncate">
                      {service.description}
                    </TableCell>
                    <TableCell className="font-mono font-medium">{service.price} ريال</TableCell>
                    <TableCell className="font-arabic">{service.duration}</TableCell>
                    <TableCell className="font-mono">
                      <Badge variant="outline" className="font-arabic">
                        {service.ordersCount} طلب
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="flex">
                          {renderStars(service.rating)}
                        </div>
                        <span className="text-sm font-medium">{service.rating}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {service.hasImage ? (
                        <Badge variant="secondary" className="font-arabic">
                          <Image className="h-3 w-3 ml-1" />
                          متوفرة
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="font-arabic">
                          غير متوفرة
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={service.isActive}
                          onCheckedChange={() => toggleServiceStatus(service.id)}
                        />
                        {service.isActive ? (
                          <Eye className="h-4 w-4 text-green-600" />
                        ) : (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleEditClick(service)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          onClick={() => handleDeleteClick(service)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleImageUpload(service)}
                        >
                          <Image className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <AddServiceModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={handleAddService}
      />

      <EditServiceModal
        service={selectedService}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onUpdate={handleUpdateService}
      />

      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={() => {
          if (selectedService) {
            handleDeleteService(selectedService.id);
            setIsDeleteModalOpen(false);
          }
        }}
        title="حذف الخدمة"
        description={`هل أنت متأكد من حذف الخدمة "${selectedService?.name}"؟ لا يمكن التراجع عن هذا الإجراء.`}
      />
    </div>
  );
};

export default Services;