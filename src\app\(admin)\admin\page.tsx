import AdminDashboard from "@/pages/admin/AdminDashboard";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "لوحة الإدارة - نظام إدارة الكراجات",
  description: "لوحة التحكم الرئيسية للمديرين - إدارة المستخدمين والكراجات والنظام",
};

// Server component with SSR data fetching for admin dashboard
export default async function AdminDashboardPage() {
  const adminDashboardData = await getAdminDashboardData();

  return <AdminDashboard initialData={adminDashboardData} />;
}

// Mock server-side data fetching for admin dashboard
async function getAdminDashboardData() {
  // In a real app, this would fetch from your database
  return {
    platform: {
      name: "منصة إدارة الكراجات",
      version: "2.1.0",
      totalUsers: 1247,
      totalGarages: 89,
      totalRevenue: 125000
    },
    stats: {
      totalUsers: 1247,
      activeUsers: 892,
      totalGarages: 89,
      activeGarages: 76,
      totalOrders: 5643,
      pendingOrders: 234,
      monthlyRevenue: 125000,
      supportTickets: 45
    },
    recentActivity: [
      {
        id: 1,
        type: "new_garage_registration",
        message: "تم تسجيل كراج جديد: مركز الخليج للصيانة",
        timestamp: new Date().toISOString(),
        severity: "info"
      },
      {
        id: 2,
        type: "user_complaint",
        message: "شكوى جديدة من المستخدم أحمد محمد",
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        severity: "warning"
      },
      {
        id: 3,
        type: "payment_processed",
        message: "تم معالجة دفعة بقيمة 2,500 ريال",
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        severity: "success"
      }
    ]
  };
}
