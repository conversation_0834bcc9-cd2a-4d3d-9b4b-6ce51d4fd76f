import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, Legend } from 'recharts';

const monthlyOrdersData = [
  { month: 'يناير', orders: 45 },
  { month: 'فبراير', orders: 52 },
  { month: 'مارس', orders: 48 },
  { month: 'أبريل', orders: 61 },
  { month: 'مايو', orders: 55 },
  { month: 'يونيو', orders: 67 }
];

const orderStatusData = [
  { name: 'جديد', value: 15, color: 'hsl(var(--primary))' },
  { name: 'قيد الإصلاح', value: 25, color: 'hsl(var(--secondary))' },
  { name: 'جاهز للاستلام', value: 18, color: 'hsl(var(--accent))' },
  { name: 'مؤجل', value: 7, color: 'hsl(var(--muted))' }
];

const servicesData = [
  { service: 'صيانة شاملة', count: 32 },
  { service: 'تغيير زيت', count: 28 },
  { service: 'إطارات', count: 22 },
  { service: 'مكابح', count: 18 },
  { service: 'تكييف', count: 15 }
];

export function StatisticsCharts() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {/* Monthly Orders Chart */}
      <Card className="lg:col-span-2 bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">الطلبات الشهرية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={monthlyOrdersData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="month" 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    fontFamily: 'var(--font-arabic)'
                  }}
                />
                <Bar 
                  dataKey="orders" 
                  fill="hsl(var(--primary))"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Order Status Distribution */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">توزيع حالة الطلبات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={orderStatusData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {orderStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    fontFamily: 'var(--font-arabic)'
                  }}
                />
                <Legend 
                  wrapperStyle={{
                    fontFamily: 'var(--font-arabic)',
                    fontSize: '14px'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Most Requested Services */}
      <Card className="lg:col-span-3 bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">الخدمات الأكثر طلباً</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={servicesData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  type="number"
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  type="category"
                  dataKey="service"
                  className="text-xs"
                  tick={{ fontSize: 12 }}
                  width={100}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    fontFamily: 'var(--font-arabic)'
                  }}
                />
                <Bar 
                  dataKey="count" 
                  fill="hsl(var(--secondary))"
                  radius={[0, 4, 4, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}