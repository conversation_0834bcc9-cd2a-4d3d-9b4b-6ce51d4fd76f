import Vehicles from "@/pages/Vehicles";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "مركباتي - نظام إدارة الكراجات",
  description: "إدارة وعرض جميع مركباتك المسجلة في النظام",
};

// Server component with SSR data fetching
export default async function VehiclesPage() {
  const vehiclesData = await getVehiclesData();

  return <Vehicles initialData={vehiclesData} />;
}

// Mock server-side data fetching for vehicles
async function getVehiclesData() {
  // In a real app, this would fetch from your database
  return {
    vehicles: [
      {
        id: 1,
        make: "تويوتا",
        model: "كامري",
        year: 2022,
        plateNumber: "أ ب ج 1234",
        color: "أبيض",
        status: "active",
        lastService: "2024-01-15",
        nextService: "2024-04-15",
        mileage: 25000,
        image: "/api/placeholder/300/200"
      },
      {
        id: 2,
        make: "هوندا",
        model: "أكورد",
        year: 2021,
        plateNumber: "د هـ و 5678",
        color: "أسود",
        status: "maintenance",
        lastService: "2024-01-10",
        nextService: "2024-04-10",
        mileage: 32000,
        image: "/api/placeholder/300/200"
      },
      {
        id: 3,
        make: "نيسان",
        model: "التيما",
        year: 2023,
        plateNumber: "ز ح ط 9012",
        color: "فضي",
        status: "pending",
        lastService: "2023-12-20",
        nextService: "2024-03-20",
        mileage: 18000,
        image: "/api/placeholder/300/200"
      }
    ]
  };
}
