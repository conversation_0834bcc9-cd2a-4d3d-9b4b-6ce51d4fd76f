import { <PERSON>, <PERSON>, <PERSON>, Sun } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useTheme } from "next-themes";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function GarageHeader() {
  const { setTheme } = useTheme();

  return (
    <header className="h-16 border-b border-border/50 bg-card/80 backdrop-blur-md px-4 md:px-6 flex items-center justify-between gap-4 shadow-sm">
      {/* Right side - Trigger and Search */}
      <div className="flex items-center gap-4 flex-1">
        <SidebarTrigger className="hover:bg-muted/80 transition-colors" />
        
        <div className="relative max-w-md flex-1 md:flex-initial">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="البحث في الطلبات والعملاء..."
            className="pr-10 bg-muted/50 border-muted focus:bg-background focus:border-primary/30 transition-all"
          />
        </div>
      </div>

      {/* Left side - Actions */}
      <div className="flex items-center gap-2">
        {/* Notifications */}
        <Button
          variant="ghost"
          size="icon"
          className="relative hover:bg-muted/80 transition-all duration-200 hover:scale-105"
        >
          <Bell className="w-5 h-5" />
          <Badge 
            variant="destructive" 
            className="absolute -top-1 -left-1 w-5 h-5 text-xs p-0 flex items-center justify-center rounded-full"
          >
            5
          </Badge>
        </Button>

        {/* Theme Toggle */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="icon"
              className="hover:bg-muted/80 transition-all duration-200 hover:scale-105"
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">تبديل المظهر</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="font-arabic">
            <DropdownMenuItem onClick={() => setTheme("light")}>
              فاتح
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("dark")}>
              داكن
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme("system")}>
              النظام
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}