"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  FileText,
  Wrench,
  Car,
  Users,
  BarChart3,
  Bell,
  Settings,
  LogOut,
  Building2,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  useSidebar,
  SidebarRail,
} from "@/components/ui/sidebar";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

// Garage owner menu items
const garageMenuItems = [
  { title: "لوحة التحكم", url: "/garage-dashboard", icon: Home, badge: null },
  { title: "الطلبات", url: "/garage-dashboard/orders", icon: FileText, badge: "3" },
  { title: "الخدمات", url: "/garage-dashboard/services", icon: Wrench, badge: null },
  { title: "المركبات داخل الكراج", url: "/garage-dashboard/vehicles", icon: Car, badge: "7" },
  { title: "العملاء والتقييمات", url: "/garage-dashboard/customers", icon: Users, badge: null },
  { title: "التقارير والإحصائيات", url: "/garage-dashboard/reports", icon: BarChart3, badge: null },
  { title: "الإشعارات", url: "/garage-dashboard/notifications", icon: Bell, badge: "2" },
  { title: "الإعدادات", url: "/garage-dashboard/settings", icon: Settings, badge: null },
];

export function GarageSidebar() {
  const { state, isMobile } = useSidebar();
  const pathname = usePathname();
  const isCollapsed = state === "collapsed";

  const isActive = (path: string) => pathname === path;

  return (
    <Sidebar
      side="right"
      className="border-l border-border/50 bg-gradient-to-b from-card via-card/95 to-muted/20 backdrop-blur-sm"
      collapsible="offcanvas"
    >
      {/* Header */}
      <SidebarHeader className="p-4 md:p-6 border-b border-border/30">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-2xl flex items-center justify-center shadow-lg ring-2 ring-blue-600/20">
            <Building2 className="w-6 h-6 text-white" />
          </div>
          {(!isCollapsed || isMobile) && (
            <div className="flex-1 min-w-0">
              <h1 className="text-xl font-bold  truncate bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                مركز الصيانة
              </h1>
              <p className="text-sm text-muted-foreground">لوحة التحكم المتقدمة</p>
            </div>
          )}
        </div>
      </SidebarHeader>

      {/* Content */}
      <SidebarContent className="px-2 md:px-3 py-4">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {garageMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={isActive(item.url)}
                    className={`
                      w-full h-14 rounded-2xl transition-all duration-300 group relative overflow-hidden
                      ${
                        isActive(item.url)
                          ? "bg-gradient-to-r from-blue-600/20 via-blue-500/15 to-blue-400/10 text-blue-700 dark:text-blue-300 border border-blue-500/30 shadow-lg shadow-blue-500/20 scale-[1.02]"
                          : "hover:bg-gradient-to-r hover:from-muted/70 hover:to-muted/50 text-muted-foreground hover:text-foreground hover:scale-[1.01] hover:shadow-md"
                      }
                      touch-target
                    `}
                    tooltip={isCollapsed && !isMobile ? item.title : undefined}
                  >
                    <Link
                      href={item.url}
                      className="flex items-center gap-4 w-full justify-start px-4 relative"
                    >
                      <item.icon
                        className={`w-6 h-6 flex-shrink-0 transition-all duration-300 ${
                          isActive(item.url) 
                            ? "text-blue-600 dark:text-blue-400 scale-110" 
                            : "group-hover:scale-105"
                        }`}
                      />
                      {(!isCollapsed || isMobile) && (
                        <>
                          <span className="font-medium text-sm truncate">
                            {item.title}
                          </span>
                          {item.badge && (
                            <Badge 
                              variant="secondary" 
                              className={`mr-auto text-xs px-2 py-1 rounded-full ${
                                isActive(item.url)
                                  ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300"
                                  : "bg-muted text-muted-foreground"
                              }`}
                            >
                              {item.badge}
                            </Badge>
                          )}
                        </>
                      )}
                      {isCollapsed && !isMobile && item.badge && (
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full border-2 border-background"></div>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      {/* Footer */}
      <SidebarFooter className="p-3 md:p-4 border-t border-border/30">
        <div className="flex items-center gap-3 mb-4 p-3 rounded-xl bg-gradient-to-r from-muted/50 to-muted/30 border border-border/20">
          <Avatar className="w-12 h-12 ring-2 ring-blue-500/30 shadow-lg">
            <AvatarImage src="/api/placeholder/48/48" />
            <AvatarFallback className="bg-gradient-to-br from-blue-600 to-blue-800 text-white font-bold text-lg">
              ص.م
            </AvatarFallback>
          </Avatar>
          {(!isCollapsed || isMobile) && (
            <div className="flex-1 min-w-0">
              <p className="font-semibold text-sm text-foreground truncate">
                صالح محمد الأحمد
              </p>
              <p className="text-xs text-muted-foreground truncate">
                مالك الكراج
              </p>
            </div>
          )}
        </div>

        <SidebarMenuItem>
          <SidebarMenuButton
            onClick={() => alert('تسجيل الخروج')}
            className="w-full h-12 rounded-xl text-destructive hover:bg-destructive/10 hover:text-destructive justify-start px-4 transition-all duration-300 hover:scale-105"
            tooltip={isCollapsed && !isMobile ? "تسجيل الخروج" : undefined}
          >
            <LogOut className="w-5 h-5 flex-shrink-0" />
            {(!isCollapsed || isMobile) && (
              <span className="font-medium text-sm">
                تسجيل الخروج
              </span>
            )}
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}