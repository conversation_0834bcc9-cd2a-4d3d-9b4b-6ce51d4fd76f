"use client";

import React, { createContext, useContext, useState } from 'react';

export interface Notification {
  id: number;
  type: 'maintenance' | 'payment' | 'license' | 'repair' | 'general';
  title: string;
  description: string;
  timestamp: string;
  status: 'new' | 'read';
  priority: 'low' | 'medium' | 'high';
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: number) => void;
  markAllAsRead: () => void;
  deleteNotification: (id: number) => void;
  deleteAllNotifications: () => void;
  addNotification: (notification: Omit<Notification, 'id'>) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Mock data for notifications
const mockNotifications: Notification[] = [
  {
    id: 1,
    type: "license",
    title: "انتهاء الترخيص قريباً",
    description: "تبقّى 7 أيام على انتهاء ترخيص مركبتك رقم ABC-123",
    timestamp: "2025-01-08T10:30:00",
    status: "new",
    priority: "high"
  },
  {
    id: 2,
    type: "maintenance",
    title: "موعد الصيانة الدورية",
    description: "حان وقت الصيانة الدورية لمركبتك تويوتا كامري 2022",
    timestamp: "2025-01-07T14:15:00",
    status: "read",
    priority: "medium"
  },
  {
    id: 3,
    type: "payment",
    title: "دفعة مستحقة",
    description: "لديك دفعة مستحقة بقيمة 1,500 ريال لخدمة إصلاح المحرك",
    timestamp: "2025-01-06T09:20:00",
    status: "new",
    priority: "high"
  },
  {
    id: 4,
    type: "repair",
    title: "اكتمال أعمال الإصلاح",
    description: "تم الانتهاء من إصلاح نظام الفرامل في مركبتك",
    timestamp: "2025-01-05T16:45:00",
    status: "read",
    priority: "low"
  },
  {
    id: 5,
    type: "general",
    title: "تحديث النظام",
    description: "تم تحديث نظام إدارة خدمات السيارات بميزات جديدة",
    timestamp: "2025-01-04T11:00:00",
    status: "read",
    priority: "low"
  }
];

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);

  const unreadCount = notifications.filter(n => n.status === 'new').length;

  const markAsRead = (id: number) => {
    setNotifications(prev => prev.map(n => 
      n.id === id ? { ...n, status: 'read' as const } : n
    ));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, status: 'read' as const })));
  };

  const deleteNotification = (id: number) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const deleteAllNotifications = () => {
    setNotifications([]);
  };

  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const newId = Math.max(...notifications.map(n => n.id), 0) + 1;
    setNotifications(prev => [{ ...notification, id: newId }, ...prev]);
  };

  return (
    <NotificationContext.Provider value={{
      notifications,
      unreadCount,
      markAsRead,
      markAllAsRead,
      deleteNotification,
      deleteAllNotifications,
      addNotification
    }}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}