import { SidebarProvider } from "@/components/ui/sidebar";
import { GarageSidebar } from "@/components/layout/GarageSidebar";
import { GarageHeader } from "@/components/layout/GarageHeader";

export default function GarageLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div dir="rtl" className="min-h-screen bg-background font-arabic">
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <GarageSidebar />
          <div className="flex-1 flex flex-col overflow-hidden">
            <GarageHeader />
            <main className="flex-1 overflow-auto bg-gradient-to-br from-background via-background/50 to-muted/20">
              {children}
            </main>
          </div>
        </div>
      </SidebarProvider>
    </div>
  );
}
