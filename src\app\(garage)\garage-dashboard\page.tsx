import GarageDashboard from "@/pages/GarageDashboard";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "لوحة تحكم الكراج - نظام إدارة الكراجات",
  description: "لوحة التحكم الرئيسية لأصحاب الكراجات - إدارة الطلبات والعملاء والخدمات",
};

// Server component with SSR data fetching for garage dashboard
export default async function GarageDashboardPage() {
  const garageDashboardData = await getGarageDashboardData();

  return <GarageDashboard initialData={garageDashboardData} />;
}

// Mock server-side data fetching for garage dashboard
async function getGarageDashboardData() {
  // In a real app, this would fetch from your database
  return {
    garage: {
      name: "مركز الصيانة المتطور",
      location: "الرياض، حي النخيل",
      rating: 4.8,
      totalReviews: 245
    },
    stats: {
      totalOrders: 156,
      pendingOrders: 12,
      completedOrders: 144,
      monthlyRevenue: 45000,
      activeVehicles: 8,
      totalCustomers: 89
    },
    recentOrders: [
      {
        id: 1,
        customerName: "محمد أحمد",
        vehicleInfo: "تويوتا كامري 2022",
        serviceType: "صيانة دورية",
        status: "in_progress",
        estimatedCompletion: "2024-01-20",
        amount: 450
      },
      {
        id: 2,
        customerName: "فاطمة علي",
        vehicleInfo: "هوندا أكورد 2021",
        serviceType: "إصلاح محرك",
        status: "pending",
        estimatedCompletion: "2024-01-22",
        amount: 1200
      }
    ]
  };
}
