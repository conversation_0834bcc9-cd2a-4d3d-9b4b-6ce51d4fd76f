"use client";

import { useState } from "react";
import { <PERSON>, Bell, <PERSON>ff, Trash2, <PERSON>Check, <PERSON>tings, Car, CreditCard, FileText, <PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle, Gift, Shield } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { useNotifications } from "@/contexts/NotificationContext";

const customerNotificationTypes = [
  { id: "maintenance", label: "تذكير الصيانة", icon: Wrench, color: "bg-gradient-to-br from-primary to-primary-dark" },
  { id: "payment", label: "الفواتير والدفعات", icon: CreditCard, color: "bg-gradient-to-br from-green-500 to-green-600" },
  { id: "license", label: "انتهاء الترخيص", icon: FileText, color: "bg-gradient-to-br from-orange-500 to-orange-600" },
  { id: "repair", label: "تحديثات الإصلاح", icon: Car, color: "bg-gradient-to-br from-primary to-primary-dark" },
  { id: "offers", label: "العروض والخصومات", icon: Gift, color: "bg-gradient-to-br from-primary-light to-primary" },
  { id: "insurance", label: "التأمين", icon: Shield, color: "bg-gradient-to-br from-primary to-primary-dark" },
  { id: "general", label: "إشعارات عامة", icon: AlertTriangle, color: "bg-gradient-to-br from-muted-foreground to-muted-foreground" }
];

interface CustomerNotificationCardProps {
  notification: {
    id: number;
    title: string;
    description: string;
    timestamp: string;
    priority: string;
    status: string;
    type: string;
  };
  onMarkAsRead: (id: number) => void;
  onDelete: (id: number) => void;
}

function CustomerNotificationCard({ notification, onMarkAsRead, onDelete }: CustomerNotificationCardProps) {
  const typeInfo = customerNotificationTypes.find(t => t.id === notification.type);
  const Icon = typeInfo?.icon || Bell;
  
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "الآن";
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    if (diffInHours < 48) return "أمس";
    return date.toLocaleDateString('ar-SA', { 
      day: 'numeric', 
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityStyle = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-destructive/20 bg-destructive/5';
      case 'medium': return 'border-orange-500/20 bg-orange-500/5';
      default: return 'border-border bg-card/50';
    }
  };

  return (
    <Card className={`group transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 ${
      notification.status === 'new' ? 'ring-2 ring-primary/30 shadow-md' : ''
    } ${getPriorityStyle(notification.priority)} backdrop-blur-sm`}>
      <CardContent className="p-5">
        <div className="flex items-start gap-4">
          {/* Icon with soft gradient */}
          <div className={`w-14 h-14 rounded-2xl ${typeInfo?.color || 'bg-gradient-to-br from-primary to-primary-dark'} 
            flex items-center justify-center text-white shadow-lg group-hover:scale-105 transition-transform duration-200 flex-shrink-0`}>
            <Icon className="w-7 h-7" />
          </div>
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-3 mb-3">
              <h3 className="font-almarai font-bold text-lg text-foreground leading-tight">
                {notification.title}
              </h3>
              <div className="flex items-center gap-2 flex-shrink-0">
                {notification.status === 'new' && (
                  <Badge className="bg-gradient-to-r from-primary to-primary-dark text-primary-foreground border-0 shadow-sm">
                    جديد
                  </Badge>
                )}
                {notification.priority === 'high' && (
                  <Badge variant="destructive" className="bg-gradient-to-r from-destructive to-destructive border-0">
                    عاجل
                  </Badge>
                )}
              </div>
            </div>
            
            <p className="text-muted-foreground text-sm leading-relaxed mb-4 font-arabic">
              {notification.description}
            </p>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground font-arabic bg-muted/50 px-3 py-1 rounded-full">
                {formatTime(notification.timestamp)}
              </span>
              
              <div className="flex items-center gap-2">
                {notification.status === 'new' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onMarkAsRead(notification.id)}
                    className="h-8 px-3 text-xs bg-primary/10 hover:bg-primary/20 text-primary border border-primary/20"
                  >
                    <CheckCheck className="w-4 h-4 ml-1" />
                    تحديد كمقروء
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(notification.id)}
                  className="h-8 px-3 text-xs text-muted-foreground hover:text-destructive hover:bg-destructive/10"
                >
                  <Trash2 className="w-4 h-4 ml-1" />
                  حذف
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface CustomerNotificationSettingsProps {
  settings: Record<string, boolean>;
  onToggle: (type: string) => void;
}

function CustomerNotificationSettings({ settings, onToggle }: CustomerNotificationSettingsProps) {
  return (
    <Card className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border-border/50">
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary-dark rounded-xl flex items-center justify-center shadow-lg">
            <Settings className="w-5 h-5 text-primary-foreground" />
          </div>
          <h2 className="text-xl font-almarai font-bold">إعدادات الإشعارات</h2>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {customerNotificationTypes.map((type) => {
          const Icon = type.icon;
          return (
            <div key={type.id} className="flex items-center justify-between p-4 rounded-xl bg-muted/20 hover:bg-muted/40 transition-colors border border-border/30">
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 ${type.color} rounded-xl flex items-center justify-center text-white shadow-md`}>
                  <Icon className="w-5 h-5" />
                </div>
                <div>
                  <span className="font-arabic font-medium text-foreground">{type.label}</span>
                  <p className="text-xs text-muted-foreground font-arabic">
                    {type.id === "maintenance" ? "تنبيهات مواعيد الصيانة" :
                     type.id === "payment" ? "إشعارات الفواتير والدفعات" :
                     type.id === "offers" ? "العروض الخاصة والخصومات" :
                     "إشعارات هامة"}
                  </p>
                </div>
              </div>
              <Switch
                checked={settings[type.id] ?? true}
                onCheckedChange={() => onToggle(type.id)}
                className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-primary data-[state=checked]:to-primary-dark"
              />
            </div>
          );
        })}
        
        <div className="mt-6 p-4 bg-gradient-to-r from-primary/5 to-primary/10 rounded-xl border border-primary/20">
          <div className="flex items-center gap-2 mb-2">
            <Bell className="w-4 h-4 text-primary" />
            <span className="font-arabic font-medium text-foreground">إعدادات الإشعارات المباشرة</span>
          </div>
          <p className="text-xs text-muted-foreground font-arabic mb-3">
            اضغط هنا لإدارة إعدادات الإشعارات المباشرة على جهازك
          </p>
          <Button variant="outline" size="sm" className="text-primary border-primary/20 hover:bg-primary/5">
            إدارة الإشعارات المباشرة
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export default function CustomerNotifications() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications
  } = useNotifications();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedType, setSelectedType] = useState("all");
  const [notificationSettings, setNotificationSettings] = useState<Record<string, boolean>>({});

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.includes(searchQuery) || 
                         notification.description.includes(searchQuery);
    const matchesType = selectedType === "all" || notification.type === selectedType;
    return matchesSearch && matchesType;
  });

  const handleMarkAllAsRead = () => {
    markAllAsRead();
    toast({
      title: "تم تحديد جميع الإشعارات كمقروءة",
      description: "تم تحديث حالة جميع الإشعارات بنجاح"
    });
  };

  const handleDeleteAll = () => {
    deleteAllNotifications();
    toast({
      title: "تم حذف جميع الإشعارات",
      description: "تم حذف جميع الإشعارات من النظام"
    });
  };

  const handleMarkAsRead = (id: number) => {
    markAsRead(id);
    toast({
      title: "تم تحديد الإشعار كمقروء",
      description: "تم تحديث حالة الإشعار بنجاح"
    });
  };

  const handleDelete = (id: number) => {
    deleteNotification(id);
    toast({
      title: "تم حذف الإشعار",
      description: "تم حذف الإشعار من النظام"
    });
  };

  const handleSettingToggle = (type: string) => {
    setNotificationSettings(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
    toast({
      title: "تم تحديث الإعدادات",
      description: `تم ${notificationSettings[type] ? 'إيقاف' : 'تفعيل'} إشعارات ${customerNotificationTypes.find(t => t.id === type)?.label}`
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/30">
      <div className="container mx-auto p-4 md:p-6 space-y-6 max-w-6xl">
        {/* Header with soft gradient background */}
        <div className="space-y-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gradient-to-br from-primary via-primary to-primary-dark rounded-2xl flex items-center justify-center shadow-xl">
                <Bell className="w-8 h-8 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-almarai font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                  إشعاراتي
                </h1>
                <p className="text-muted-foreground font-arabic text-lg">
                  {unreadCount > 0 ? `${unreadCount} إشعار جديد` : 'لا توجد إشعارات جديدة'}
                </p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                onClick={handleMarkAllAsRead}
                disabled={unreadCount === 0}
                className="flex items-center gap-2 bg-card/50 border-border/50 hover:bg-primary/10 hover:border-primary/30"
              >
                <CheckCheck className="w-4 h-4" />
                تحديد الكل كمقروء
              </Button>
              <Button
                variant="outline"
                onClick={handleDeleteAll}
                disabled={notifications.length === 0}
                className="flex items-center gap-2 bg-card/50 border-border/50 hover:bg-destructive/10 hover:border-destructive/30 hover:text-destructive"
              >
                <Trash2 className="w-4 h-4" />
                حذف جميع الإشعارات
              </Button>
            </div>
          </div>

          {/* Search and Filters with enhanced styling */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <Input
                placeholder="البحث في الإشعارات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-12 font-arabic h-12 bg-card/50 border-border/50 focus:border-primary/50 focus:ring-primary/20"
              />
            </div>
            
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full md:w-64 h-12 bg-card/50 border-border/50">
                <SelectValue placeholder="تصفية حسب النوع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الإشعارات</SelectItem>
                {customerNotificationTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Main Content with enhanced tabs */}
        <Tabs defaultValue="notifications" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 bg-card/50 border border-border/50 backdrop-blur-sm">
            <TabsTrigger value="notifications" className="font-arabic data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              الإشعارات ({filteredNotifications.length})
            </TabsTrigger>
            <TabsTrigger value="settings" className="font-arabic data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              الإعدادات
            </TabsTrigger>
          </TabsList>

          <TabsContent value="notifications" className="space-y-4">
            {filteredNotifications.length === 0 ? (
              <Card className="bg-gradient-to-br from-card/60 to-card/30 backdrop-blur-sm border-border/50">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <div className="w-20 h-20 bg-gradient-to-br from-muted to-muted/50 rounded-full flex items-center justify-center mb-6">
                    <BellOff className="w-10 h-10 text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-almarai font-bold text-foreground mb-3">
                    لا توجد إشعارات
                  </h3>
                  <p className="text-muted-foreground text-center font-arabic max-w-md">
                    {searchQuery || selectedType !== "all" 
                      ? "لم يتم العثور على إشعارات تطابق البحث المحدد"
                      : "لا توجد إشعارات في الوقت الحالي. ستظهر هنا عندما تتوفر"
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredNotifications.map((notification) => (
                  <CustomerNotificationCard
                    key={notification.id}
                    notification={notification}
                    onMarkAsRead={handleMarkAsRead}
                    onDelete={handleDelete}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="settings">
            <CustomerNotificationSettings
              settings={notificationSettings}
              onToggle={handleSettingToggle}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}