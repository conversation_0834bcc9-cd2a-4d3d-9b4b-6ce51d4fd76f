import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Bell,
  Search,
  CheckCheck,
  Trash2,
  User,
  Building2,
  CreditCard,
  Star,
  Shield,
  Clock,
  Send,
  Eye,
  MessageSquare,
  Plus,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Mock notifications data
const mockNotifications = [
  {
    id: "1",
    type: "support",
    icon: MessageSquare,
    title: "طلب دعم جديد",
    description: "عميل يطلب مساعدة في مشكلة تقنية بالتطبيق",
    date: "2024-01-15T10:30:00",
    status: "unread",
    priority: "high",
    user: "أحمد محمد",
    category: "technical"
  },
  {
    id: "2", 
    type: "payment",
    icon: CreditCard,
    title: "فشل عملية دفع",
    description: "فشل في معالجة عملية دفع لكراج الشرق",
    date: "2024-01-15T09:15:00",
    status: "unread",
    priority: "high",
    user: "كراج الشرق",
    category: "financial"
  },
  {
    id: "3",
    type: "registration",
    icon: Building2,
    title: "تسجيل كراج جديد",
    description: "كراج الخليج قدم طلب للانضمام للمنصة",
    date: "2024-01-15T08:45:00",
    status: "read",
    priority: "medium",
    user: "كراج الخليج",
    category: "registration"
  },
  {
    id: "4",
    type: "review",
    icon: Star,
    title: "تقييم سلبي جديد",
    description: "تقييم سلبي لكراج النور مع شكوى من العميل",
    date: "2024-01-15T07:20:00",
    status: "read",
    priority: "medium",
    user: "سارة أحمد",
    category: "reviews"
  },
  {
    id: "5",
    type: "security",
    icon: Shield,
    title: "تنبيه أمني",
    description: "محاولة دخول مشبوهة لحساب إداري",
    date: "2024-01-15T06:10:00",
    status: "unread",
    priority: "critical",
    user: "النظام",
    category: "security"
  },
  {
    id: "6",
    type: "user",
    icon: User,
    title: "تسجيل مستخدم جديد",
    description: "انضمام 15 مستخدم جديد اليوم",
    date: "2024-01-15T05:30:00",
    status: "read",
    priority: "low",
    user: "النظام",
    category: "registration"
  },
];

const notificationCategories = [
  { value: "all", label: "جميع الإشعارات", count: mockNotifications.length },
  { value: "support", label: "طلبات الدعم", count: 1 },
  { value: "financial", label: "التنبيهات المالية", count: 1 },
  { value: "registration", label: "التسجيلات", count: 2 },
  { value: "reviews", label: "المراجعات والتقييمات", count: 1 },
  { value: "security", label: "التنبيهات الأمنية", count: 1 },
];

const priorityColors = {
  critical: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300",
  high: "bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300",
  medium: "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300",
  low: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300",
};

const typeIcons = {
  support: MessageSquare,
  payment: CreditCard,
  registration: Building2,
  review: Star,
  security: Shield,
  user: User,
};

export default function Notifications() {
  const [notifications, setNotifications] = useState(mockNotifications);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [newNotification, setNewNotification] = useState({
    title: "",
    message: "",
    audience: "all",
    priority: "medium"
  });
  const { toast } = useToast();

  const filteredNotifications = notifications.filter(notification => {
    const matchesCategory = selectedCategory === "all" || notification.category === selectedCategory;
    const matchesSearch = notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         notification.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const unreadCount = notifications.filter(n => n.status === "unread").length;

  const handleMarkAllRead = () => {
    setNotifications(notifications.map(n => ({ ...n, status: "read" })));
    toast({
      title: "تم تحديث الإشعارات",
      description: "تم تمييز جميع الإشعارات كمقروءة",
    });
  };

  const handleMarkAsRead = (id: string) => {
    setNotifications(notifications.map(n => 
      n.id === id ? { ...n, status: "read" } : n
    ));
  };

  const handleDelete = (id: string) => {
    setNotifications(notifications.filter(n => n.id !== id));
    toast({
      title: "تم حذف الإشعار",
      description: "تم حذف الإشعار بنجاح",
    });
  };

  const handleSendNotification = () => {
    if (!newNotification.title || !newNotification.message) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "تم إرسال الإشعار",
      description: `تم إرسال الإشعار إلى ${newNotification.audience === "all" ? "جميع المستخدمين" : "المجموعة المحددة"}`,
    });

    setNewNotification({ title: "", message: "", audience: "all", priority: "medium" });
  };

  const getTimeAgo = (date: string) => {
    const now = new Date();
    const notificationDate = new Date(date);
    const diffInHours = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "منذ قليل";
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    return `منذ ${Math.floor(diffInHours / 24)} يوم`;
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground font-arabic">الإشعارات</h1>
          <p className="text-muted-foreground font-arabic">
            إدارة جميع الإشعارات والرسائل العامة ({unreadCount} غير مقروء)
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button className="font-arabic">
                <Plus className="w-4 h-4 ml-2" />
                إشعار جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="font-arabic">إرسال إشعار جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label className="font-arabic">العنوان</Label>
                  <Input
                    value={newNotification.title}
                    onChange={(e) => setNewNotification({...newNotification, title: e.target.value})}
                    placeholder="عنوان الإشعار"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="font-arabic">الرسالة</Label>
                  <Textarea
                    value={newNotification.message}
                    onChange={(e) => setNewNotification({...newNotification, message: e.target.value})}
                    placeholder="محتوى الرسالة"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="font-arabic">الجمهور المستهدف</Label>
                  <Select value={newNotification.audience} onValueChange={(value: string) => setNewNotification({...newNotification, audience: value})}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">جميع المستخدمين</SelectItem>
                      <SelectItem value="garages">أصحاب الكراجات</SelectItem>
                      <SelectItem value="customers">العملاء</SelectItem>
                      <SelectItem value="admins">الإداريين</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="font-arabic">الأولوية</Label>
                  <Select value={newNotification.priority} onValueChange={(value: string) => setNewNotification({...newNotification, priority: value})}>
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">منخفضة</SelectItem>
                      <SelectItem value="medium">متوسطة</SelectItem>
                      <SelectItem value="high">عالية</SelectItem>
                      <SelectItem value="critical">حرجة</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={handleSendNotification} className="w-full font-arabic">
                  <Send className="w-4 h-4 ml-2" />
                  إرسال الإشعار
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          <Button variant="outline" onClick={handleMarkAllRead} className="font-arabic">
            <CheckCheck className="w-4 h-4 ml-2" />
            تمييز الكل كمقروء
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="البحث في الإشعارات..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pr-10"
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          {notificationCategories.map((category) => (
            <Button
              key={category.value}
              variant={selectedCategory === category.value ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.value)}
              className="font-arabic"
            >
              {category.label}
              {category.count > 0 && (
                <Badge variant="secondary" className="mr-2">
                  {category.count}
                </Badge>
              )}
            </Button>
          ))}
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-4">
        {filteredNotifications.length === 0 ? (
          <Card className="p-8 text-center">
            <Bell className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2 font-arabic">لا توجد إشعارات</h3>
            <p className="text-muted-foreground font-arabic">لم يتم العثور على إشعارات تطابق المعايير المحددة</p>
          </Card>
        ) : (
          filteredNotifications.map((notification) => {
            const IconComponent = typeIcons[notification.type as keyof typeof typeIcons] || Bell;
            return (
              <Card
                key={notification.id}
                className={`transition-all duration-200 hover:shadow-md ${
                  notification.status === "unread" 
                    ? "bg-blue-50/50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800/30" 
                    : "bg-card"
                }`}
              >
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 ${
                      notification.priority === "critical" 
                        ? "bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400"
                        : notification.priority === "high"
                        ? "bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400"  
                        : "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                    }`}>
                      <IconComponent className="w-6 h-6" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-4 mb-2">
                        <div>
                          <h3 className="font-semibold text-foreground font-arabic">
                            {notification.title}
                          </h3>
                          <p className="text-sm text-muted-foreground font-arabic">
                            {notification.description}
                          </p>
                        </div>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Badge className={`${priorityColors[notification.priority as keyof typeof priorityColors]} text-xs font-arabic`}>
                            {notification.priority === "critical" && "حرجة"}
                            {notification.priority === "high" && "عالية"}
                            {notification.priority === "medium" && "متوسطة"}
                            {notification.priority === "low" && "منخفضة"}
                          </Badge>
                          {notification.status === "unread" && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1 font-arabic">
                            <User className="w-4 h-4" />
                            {notification.user}
                          </span>
                          <span className="flex items-center gap-1 font-arabic">
                            <Clock className="w-4 h-4" />
                            {getTimeAgo(notification.date)}
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {notification.status === "unread" && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleMarkAsRead(notification.id)}
                              className="font-arabic"
                            >
                              <Eye className="w-4 h-4 ml-1" />
                              تمييز كمقروء
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDelete(notification.id)}
                            className="text-red-600 hover:text-red-700 font-arabic"
                          >
                            <Trash2 className="w-4 h-4 ml-1" />
                            حذف
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
}