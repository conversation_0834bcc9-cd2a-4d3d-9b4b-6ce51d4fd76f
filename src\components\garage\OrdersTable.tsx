import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Eye } from "lucide-react";

interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  vehicle: string;
  status: 'new' | 'in_progress' | 'ready' | 'postponed';
  amount: number;
}

const mockOrders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    customerName: 'أحمد محمد علي',
    vehicle: 'تويوتا كامري 2020',
    status: 'new',
    amount: 850
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    customerName: 'فاطمة حسن',
    vehicle: 'هوندا أكورد 2019',
    status: 'in_progress',
    amount: 1200
  },
  {
    id: '3',
    orderNumber: 'ORD-003',
    customerName: 'محمد سالم',
    vehicle: 'نيسان التيما 2021',
    status: 'ready',
    amount: 650
  },
  {
    id: '4',
    orderNumber: 'ORD-004',
    customerName: 'سارة أحمد',
    vehicle: 'مرسيدس C-Class 2018',
    status: 'postponed',
    amount: 2100
  },
  {
    id: '5',
    orderNumber: 'ORD-005',
    customerName: 'خالد عبدالله',
    vehicle: 'بي ام دبليو X3 2020',
    status: 'in_progress',
    amount: 1750
  }
];

const getStatusBadge = (status: Order['status']) => {
  const statusConfig = {
    new: { label: 'جديد', variant: 'secondary' as const },
    in_progress: { label: 'قيد الإصلاح', variant: 'default' as const },
    ready: { label: 'جاهز للاستلام', variant: 'secondary' as const },
    postponed: { label: 'مؤجل', variant: 'outline' as const }
  };

  const config = statusConfig[status];
  return (
    <Badge variant={config.variant} className="font-arabic">
      {config.label}
    </Badge>
  );
};

export function OrdersTable() {
  return (
    <Card className="bg-card/50 backdrop-blur-sm border-border/50">
      <CardHeader>
        <CardTitle className="text-foreground font-arabic">الطلبات الأخيرة</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right font-arabic">رقم الطلب</TableHead>
                <TableHead className="text-right font-arabic">اسم العميل</TableHead>
                <TableHead className="text-right font-arabic">السيارة</TableHead>
                <TableHead className="text-right font-arabic">الحالة</TableHead>
                <TableHead className="text-right font-arabic">المبلغ</TableHead>
                <TableHead className="text-right font-arabic">تفاصيل</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockOrders.map((order) => (
                <TableRow key={order.id} className="hover:bg-muted/50">
                  <TableCell className="font-mono font-medium">{order.orderNumber}</TableCell>
                  <TableCell className="font-arabic">{order.customerName}</TableCell>
                  <TableCell className="font-arabic">{order.vehicle}</TableCell>
                  <TableCell>{getStatusBadge(order.status)}</TableCell>
                  <TableCell className="font-mono">{order.amount} ريال</TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}