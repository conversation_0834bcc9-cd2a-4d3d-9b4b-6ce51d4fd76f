import { useState } from "react";
import { <PERSON><PERSON>, Clock, Phone, MessageCircle } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

interface EmergencyServiceModalProps {
  children: React.ReactNode;
}

export function EmergencyServiceModal({ children }: EmergencyServiceModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [description, setDescription] = useState("");
  const [urgencyLevel, setUrgencyLevel] = useState<"low" | "medium" | "high">("medium");
  const { toast } = useToast();

  const urgencyOptions = [
    { value: "low", label: "عادي", color: "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400" },
    { value: "medium", label: "متوسط", color: "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400" },
    { value: "high", label: "طارئ", color: "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400" }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "تم إرسال طلب الصيانة الطارئة",
      description: "سيتم التواصل معك في أقرب وقت ممكن",
    });
    setIsOpen(false);
    setDescription("");
    setUrgencyLevel("medium");
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-almarai flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
              <Wrench className="w-4 h-4 text-white" />
            </div>
            طلب صيانة طارئة
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card className="premium-card p-4 border-red-200 dark:border-red-800">
            <div className="flex items-center gap-3 mb-4">
              <Clock className="w-5 h-5 text-red-500" />
              <h3 className="font-arabic font-semibold text-red-700 dark:text-red-400">
                خدمة الصيانة الطارئة - 24/7
              </h3>
            </div>
            <p className="text-sm font-arabic text-muted-foreground">
              سيتم التواصل معك خلال 15 دقيقة لحالات الطوارئ العالية
            </p>
          </Card>

          <div className="space-y-4">
            <div>
              <label className="font-arabic font-medium block mb-3">مستوى الأولوية</label>
              <div className="flex gap-3">
                {urgencyOptions.map((option) => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => setUrgencyLevel(option.value as any)}
                    className={`flex-1 p-3 rounded-lg border transition-all font-arabic ${
                      urgencyLevel === option.value
                        ? "border-primary bg-primary/10"
                        : "border-border hover:bg-muted/50"
                    }`}
                  >
                    <Badge className={option.color}>
                      {option.label}
                    </Badge>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="font-arabic font-medium block mb-2">وصف المشكلة</label>
              <Textarea
                placeholder="اشرح المشكلة بالتفصيل... مثال: السيارة لا تعمل، صوت غريب من المحرك، تسرب زيت..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="font-arabic h-32"
                required
              />
            </div>
          </div>

          <Card className="premium-card p-4">
            <h3 className="font-arabic font-semibold mb-3">طرق التواصل المتاحة</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center gap-2 text-sm">
                <Phone className="w-4 h-4 text-green-500" />
                <span className="font-arabic">اتصال فوري</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <MessageCircle className="w-4 h-4 text-blue-500" />
                <span className="font-arabic">واتساب</span>
              </div>
            </div>
          </Card>

          <div className="flex gap-3 pt-4">
            <Button 
              type="submit" 
              className="flex-1 bg-red-600 hover:bg-red-700 text-white font-arabic gap-2"
            >
              <Wrench className="w-4 h-4" />
              إرسال طلب طارئ
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsOpen(false)}
              className="font-arabic"
            >
              إلغاء
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}