"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Car,
  Plus,
  Eye,
  Edit,
  Trash2,
  Settings,
  Search,
  Filter,
  Grid,
  List,
  MoreVertical,
  TrendingUp,
  Activity,
  Zap,
  Star,
  Award,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AddVehicleModal } from "@/components/modals/AddVehicleModal";
import { EditVehicleModal } from "@/components/modals/EditVehicleModal";
import { DeleteConfirmModal } from "@/components/modals/DeleteConfirmModal";
import { VehicleSettingsModal } from "@/components/modals/VehicleSettingsModal";
import Image from "next/image";

const vehicles = [
  {
    id: 1,
    name: "سيارتي الأساسية",
    model: "تويوتا كامري 2022",
    chassisNumber: "JTDKN3DU8E0123456",
    licensePlate: "أ ب ج 1234",
    status: "جاهزة",
    statusType: "ready" as const,
    licenseExpiry: "2025-03-15",
    image: "/api/placeholder/300/200",
    color: "أبيض لؤلؤي",
    mileage: "45,000 كم",
    lastService: "2024-10-15",
    nextService: "2024-12-15"
  },
  {
    id: 2,
    name: "سيارة العائلة",
    model: "هوندا أكورد 2021",
    chassisNumber: "1HGCV1F30LA123456",
    licensePlate: "د هـ و 5678",
    status: "قيد الصيانة",
    statusType: "maintenance" as const,
    licenseExpiry: "2024-12-20",
    image: "/api/placeholder/300/200",
    color: "أزرق معدني",
    mileage: "62,000 كم",
    lastService: "2024-11-01",
    nextService: "2025-01-01"
  },
  {
    id: 3,
    name: "سيارة العمل",
    model: "نيسان ألتيما 2020",
    chassisNumber: "1N4AL3AP8LC123456",
    licensePlate: "ز ح ط 9012",
    status: "بانتظار قطع الغيار",
    statusType: "pending" as const,
    licenseExpiry: "2024-11-30",
    image: "/api/placeholder/300/200",
    color: "رمادي",
    mileage: "78,000 كم",
    lastService: "2024-09-20",
    nextService: "2024-11-20"
  }
];

const statusColors = {
  ready: "bg-green-500/10 text-green-600",
  maintenance: "bg-primary/10 text-primary",
  pending: "bg-orange-500/10 text-orange-600",
};

export default function Vehicles() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);

  const filteredVehicles = vehicles.filter(vehicle =>
    vehicle.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    vehicle.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
    vehicle.licensePlate.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/5" dir="rtl">
      {/* Mobile App Header */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-primary/5 to-accent/10 rounded-b-[2rem] md:rounded-b-[3rem] -z-10" />
        <div className="px-4 pt-6 pb-8 md:p-8">
          {/* Mobile Header */}
          <div className="flex items-center justify-between mb-6 md:hidden">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-primary via-primary-light to-primary-dark flex items-center justify-center shadow-xl">
                <Car className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-almarai font-bold text-foreground">
                  مركباتي
                </h1>
                <p className="text-sm text-muted-foreground font-arabic">
                  {filteredVehicles.length} مركبة
                </p>
              </div>
            </div>
            <AddVehicleModal>
              <Button
                size="sm"
                className="bg-gradient-to-r from-primary to-primary-dark text-white border-0 shadow-lg rounded-xl px-4"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </AddVehicleModal>
          </div>

          {/* Desktop Header */}
          <div className="hidden md:flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-6">
                <div className="w-20 h-20 rounded-3xl bg-gradient-to-br from-primary via-primary-light to-primary-dark flex items-center justify-center shadow-2xl">
                  <Car className="w-10 h-10 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl lg:text-5xl font-almarai font-bold bg-gradient-to-r from-foreground via-primary to-primary-dark bg-clip-text text-transparent">
                    مركباتي
                  </h1>
                  <p className="text-lg text-muted-foreground font-arabic mt-2">
                    إدارة وتتبع جميع مركباتك في مكان واحد
                  </p>
                </div>
              </div>
            </div>
            <AddVehicleModal>
              <Button
                className="bg-gradient-to-r from-primary via-primary-light to-primary-dark hover:from-primary-dark hover:to-primary text-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300 px-8 py-4 rounded-2xl text-lg font-arabic gap-3"
              >
                <Plus className="w-6 h-6" />
                إضافة مركبة جديدة
              </Button>
            </AddVehicleModal>
          </div>
        </div>
      </div>

      <div className="px-4 md:px-8 -mt-4 md:-mt-8 relative z-10 space-y-6 md:space-y-8">
        {/* Mobile Stats Cards */}
        <div className="grid grid-cols-3 gap-3 md:hidden">
          <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-emerald-500/10 via-teal-500/5 to-cyan-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 via-transparent to-teal-500/20 opacity-50" />
            <CardContent className="relative p-3 text-center">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-lg mx-auto mb-2">
                <CheckCircle className="w-4 h-4 text-white" />
              </div>
              <div className="text-lg font-bold text-foreground font-almarai">
                {vehicles.filter(v => v.statusType === "ready").length}
              </div>
              <div className="text-xs text-muted-foreground font-arabic">جاهزة</div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-indigo-500/10 via-purple-500/5 to-pink-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-transparent to-purple-500/20 opacity-50" />
            <CardContent className="relative p-3 text-center">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg mx-auto mb-2">
                <Clock className="w-4 h-4 text-white" />
              </div>
              <div className="text-lg font-bold text-foreground font-almarai">
                {vehicles.filter(v => v.statusType === "maintenance").length}
              </div>
              <div className="text-xs text-muted-foreground font-arabic">صيانة</div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-500/20 via-transparent to-orange-500/20 opacity-50" />
            <CardContent className="relative p-3 text-center">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center shadow-lg mx-auto mb-2">
                <AlertTriangle className="w-4 h-4 text-white" />
              </div>
              <div className="text-lg font-bold text-foreground font-almarai">
                {vehicles.filter(v => v.statusType === "pending").length}
              </div>
              <div className="text-xs text-muted-foreground font-arabic">معلقة</div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop Stats */}
        <div className="hidden md:grid grid-cols-1 lg:grid-cols-3 gap-6 fade-in-up" style={{ animationDelay: "0.1s" }}>
          <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-emerald-500/10 via-teal-500/5 to-cyan-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 via-transparent to-teal-500/20 opacity-50" />
            <CardContent className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-xl">
                  <CheckCircle className="w-7 h-7 text-white" />
                </div>
                <Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-md">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  جاهزة
                </Badge>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-arabic font-medium text-muted-foreground">المركبات الجاهزة</h3>
                <div className="text-3xl font-bold text-foreground font-almarai">
                  {vehicles.filter(v => v.statusType === "ready").length}
                </div>
                <p className="text-xs text-muted-foreground font-arabic">من إجمالي {vehicles.length} مركبة</p>
              </div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-indigo-500/10 via-purple-500/5 to-pink-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-transparent to-purple-500/20 opacity-50" />
            <CardContent className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-xl">
                  <Clock className="w-7 h-7 text-white" />
                </div>
                <Badge className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-0 shadow-md">
                  <Activity className="w-3 h-3 mr-1" />
                  قيد الصيانة
                </Badge>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-arabic font-medium text-muted-foreground">قيد الصيانة</h3>
                <div className="text-3xl font-bold text-foreground font-almarai">
                  {vehicles.filter(v => v.statusType === "maintenance").length}
                </div>
                <p className="text-xs text-muted-foreground font-arabic">تحتاج متابعة</p>
              </div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-500/20 via-transparent to-orange-500/20 opacity-50" />
            <CardContent className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center shadow-xl">
                  <AlertTriangle className="w-7 h-7 text-white" />
                </div>
                <Badge className="bg-gradient-to-r from-amber-500 to-orange-600 text-white border-0 shadow-md">
                  <Zap className="w-3 h-3 mr-1" />
                  معلقة
                </Badge>
              </div>
              <div className="space-y-2">
                <h3 className="text-sm font-arabic font-medium text-muted-foreground">المعلقة</h3>
                <div className="text-3xl font-bold text-foreground font-almarai">
                  {vehicles.filter(v => v.statusType === "pending").length}
                </div>
                <p className="text-xs text-muted-foreground font-arabic">تحتاج إجراء</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Search and Filters */}
        <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm fade-in-up" style={{ animationDelay: "0.2s" }}>
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-50" />
          <CardContent className="relative p-4 md:p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                <Input
                  placeholder="ابحث عن مركبة..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pr-12 h-12 font-arabic bg-background/50 backdrop-blur-sm border-border/50 focus:border-primary/50 rounded-xl"
                />
              </div>

            {/* Controls */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="h-12 px-6"
              >
                <Filter className="w-5 h-5 ml-2" />
                تصفية
              </Button>

              <div className="flex border border-border rounded-lg p-1">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="h-10 px-3"
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="h-10 px-3"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
          </CardContent>
        </Card>

        {/* Enhanced Vehicles Grid/List */}
        <div className={`
          grid gap-4 md:gap-6
          ${viewMode === "grid"
            ? "grid-cols-1 md:grid-cols-2 xl:grid-cols-3"
            : "grid-cols-1"
          }
        `}>
          {filteredVehicles.map((vehicle, index) => (
            <Card
              key={vehicle.id}
              className={`
                relative overflow-hidden group hover:shadow-2xl transition-all duration-500 border-0 bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm
                ${viewMode === "list" ? "flex flex-col md:flex-row" : ""}
                fade-in-up
              `}
              style={{ animationDelay: `${(index + 3) * 0.1}s` }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-50" />

              {/* Image */}
              <div className={`
                relative overflow-hidden
                ${viewMode === "list" ? "md:w-80 h-48 md:h-auto" : "h-48 md:h-56"}
              `}>
                <Image
                  src={vehicle.image}
                  width={300}
                  height={200}
                  quality={100}
                  alt={vehicle.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                />

                {/* Enhanced Status Badge */}
                <div className="absolute top-3 left-3">
                  <Badge className={`${statusColors[vehicle.statusType]} shadow-lg backdrop-blur-sm border-0 px-3 py-1`}>
                    {vehicle.statusType === "ready" && <CheckCircle className="w-3 h-3 mr-1" />}
                    {vehicle.statusType === "maintenance" && <Clock className="w-3 h-3 mr-1" />}
                    {vehicle.statusType === "pending" && <AlertTriangle className="w-3 h-3 mr-1" />}
                    {vehicle.status}
                  </Badge>
                </div>

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />

                {/* Quick Actions Overlay */}
                <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="flex gap-2">
                    <Button size="sm" className="w-8 h-8 p-0 bg-white/20 backdrop-blur-sm border-0 hover:bg-white/30">
                      <Eye className="w-4 h-4 text-white" />
                    </Button>
                    <EditVehicleModal vehicle={vehicle}>
                      <Button size="sm" className="w-8 h-8 p-0 bg-white/20 backdrop-blur-sm border-0 hover:bg-white/30">
                        <Edit className="w-4 h-4 text-white" />
                      </Button>
                    </EditVehicleModal>
                  </div>
                </div>
              </div>

              {/* Enhanced Content */}
              <div className="relative p-4 md:p-6 flex-1">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-almarai font-bold text-lg md:text-xl text-foreground mb-1">
                      {vehicle.name}
                    </h3>
                    <p className="text-muted-foreground font-arabic text-sm md:text-base">
                      {vehicle.model}
                    </p>
                  </div>

                  {/* Mobile Actions */}
                  <div className="md:hidden">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="w-8 h-8 p-0 hover:bg-primary/10">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem
                          onClick={() => router.push(`/vehicles/${vehicle.id}`)}
                          className="font-arabic"
                        >
                          <Eye className="w-4 h-4 ml-2" />
                          عرض التفاصيل
                        </DropdownMenuItem>
                        <EditVehicleModal vehicle={vehicle}>
                          <DropdownMenuItem
                            onSelect={(e: Event) => e.preventDefault()}
                            className="font-arabic"
                          >
                            <Edit className="w-4 h-4 ml-2" />
                            تعديل
                          </DropdownMenuItem>
                        </EditVehicleModal>
                        <DeleteConfirmModal
                          title="حذف المركبة"
                          description={`هل أنت متأكد من حذف ${vehicle.name}؟ سيتم حذف جميع البيانات والصور المرتبطة بها.`}
                        >
                          <DropdownMenuItem
                            onSelect={(e: Event) => e.preventDefault()}
                            className="font-arabic text-destructive"
                          >
                            <Trash2 className="w-4 h-4 ml-2" />
                            حذف
                          </DropdownMenuItem>
                        </DeleteConfirmModal>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Enhanced Vehicle Details */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-muted/20 to-muted/10">
                    <span className="text-muted-foreground font-arabic text-sm">رقم اللوحة:</span>
                    <Badge variant="outline" className="font-medium bg-background/50">
                      {vehicle.licensePlate}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-muted/20 to-muted/10">
                    <span className="text-muted-foreground font-arabic text-sm">المسافة المقطوعة:</span>
                    <span className="font-medium text-foreground">{vehicle.mileage}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-muted/20 to-muted/10">
                    <span className="text-muted-foreground font-arabic text-sm">انتهاء الترخيص:</span>
                    <Badge className={`${
                      new Date(vehicle.licenseExpiry) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                        ? "bg-gradient-to-r from-red-500 to-pink-600 text-white"
                        : "bg-gradient-to-r from-emerald-500 to-teal-600 text-white"
                    } border-0 shadow-md`}>
                      {new Date(vehicle.licenseExpiry).toLocaleDateString('ar-SA')}
                    </Badge>
                  </div>
                </div>

                {/* Enhanced Service Info */}
                <div className="bg-gradient-to-r from-primary/5 via-primary/3 to-accent/5 rounded-xl p-4 mb-4 border border-border/30">
                  <h4 className="font-arabic font-semibold text-sm text-foreground mb-3 flex items-center gap-2">
                    <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center">
                      <Settings className="w-3 h-3 text-white" />
                    </div>
                    معلومات الصيانة
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="text-center p-3 rounded-lg bg-background/50 backdrop-blur-sm">
                      <div className="text-xs text-muted-foreground font-arabic mb-1">آخر صيانة</div>
                      <div className="font-bold text-sm text-foreground">
                        {new Date(vehicle.lastService).toLocaleDateString('ar-SA')}
                      </div>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-background/50 backdrop-blur-sm">
                      <div className="text-xs text-muted-foreground font-arabic mb-1">الصيانة القادمة</div>
                      <div className="font-bold text-sm text-foreground">
                        {new Date(vehicle.nextService).toLocaleDateString('ar-SA')}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Action Buttons */}
                <div className="flex gap-2">
                  <Button
                    className="flex-1 bg-gradient-to-r from-primary via-primary-light to-primary-dark hover:from-primary-dark hover:to-primary text-white font-arabic border-0 shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl"
                    size="sm"
                    onClick={() => router.push(`/vehicles/${vehicle.id}`)}
                  >
                    <Eye className="w-4 h-4 ml-2" />
                    <span className="hidden sm:inline">عرض التفاصيل</span>
                    <span className="sm:hidden">عرض</span>
                  </Button>
                  <EditVehicleModal vehicle={vehicle}>
                    <Button
                      variant="outline"
                      size="sm"
                      className="px-3 bg-background/50 backdrop-blur-sm border-border/50 hover:border-primary/50 rounded-xl"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                  </EditVehicleModal>
                  <VehicleSettingsModal vehicleName={vehicle.name}>
                    <Button
                      variant="outline"
                      size="sm"
                      className="px-3 bg-background/50 backdrop-blur-sm border-border/50 hover:border-primary/50 rounded-xl"
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                  </VehicleSettingsModal>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredVehicles.length === 0 && (
          <Card className="premium-card p-12 text-center fade-in-up">
            <Car className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-almarai font-bold text-lg text-card-foreground mb-2">
              لا توجد مركبات
            </h3>
            <p className="text-muted-foreground font-arabic mb-6">
              {searchQuery 
                ? "لم يتم العثور على مركبات تطابق البحث" 
                : "ابدأ بإضافة مركبتك الأولى لتتبع صيانتها وخدماتها"
              }
            </p>
            <AddVehicleModal>
              <Button className="btn-premium text-white font-arabic gap-2">
                <Plus className="w-5 h-5" />
                إضافة مركبة جديدة
              </Button>
            </AddVehicleModal>
          </Card>
        )}
      </div>
    </div>
  );
}