import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Star,
  MessageSquare,
  Trash2,
  Reply,
  Eye,
  ThumbsDown,
  AlertTriangle,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Review {
  id: string;
  customerName: string;
  garageName: string;
  garageCity: string;
  serviceName: string;
  rating: number;
  comment: string;
  createdDate: string;
  status: "visible" | "hidden" | "reported";
  adminReply?: string;
  isNegative: boolean;
}

const mockReviews: Review[] = [
  {
    id: "1",
    customerName: "أحمد محمد الأحمد",
    garageName: "كراج النجمة للصيانة",
    garageCity: "الرياض",
    serviceName: "تغيير الزيت",
    rating: 5,
    comment: "خدمة ممتازة وسرعة في الإنجاز. أنصح بالتعامل معهم.",
    createdDate: "2024-01-15",
    status: "visible",
    isNegative: false,
  },
  {
    id: "2",
    customerName: "فاطمة علي السالم",
    garageName: "مركز الأمانة لخدمات السيارات",
    garageCity: "جدة",
    serviceName: "فحص شامل",
    rating: 4,
    comment: "خدمة جيدة ولكن الانتظار كان طويلاً نوعاً ما.",
    createdDate: "2024-01-12",
    status: "visible",
    isNegative: false,
  },
  {
    id: "3",
    customerName: "محمد سعد الغامدي",
    garageName: "ورشة الإتقان للإصلاح",
    garageCity: "الدمام",
    serviceName: "إصلاح الفرامل",
    rating: 2,
    comment: "المشكلة لم تحل بشكل كامل واضطررت للعودة مرة أخرى.",
    createdDate: "2024-01-10",
    status: "visible",
    adminReply: "نعتذر عن هذه التجربة. تم التواصل مع الكراج لحل المشكلة.",
    isNegative: true,
  },
  {
    id: "4",
    customerName: "سارة أحمد الخليفة",
    garageName: "مجمع السرعة للصيانة الشاملة",
    garageCity: "مكة",
    serviceName: "صيانة التكييف",
    rating: 1,
    comment: "خدمة سيئة جداً والفنيين غير مؤهلين. لا أنصح بالتعامل معهم إطلاقاً.",
    createdDate: "2024-01-08",
    status: "reported",
    isNegative: true,
  },
  {
    id: "5",
    customerName: "خالد عبدالله النعيمي",
    garageName: "ورشة التميز للسيارات",
    garageCity: "المدينة",
    serviceName: "تبديل الإطارات",
    rating: 5,
    comment: "ممتاز جداً، خدمة سريعة ومهنية عالية.",
    createdDate: "2024-01-05",
    status: "visible",
    isNegative: false,
  },
];

const statusConfig = {
  visible: { label: "ظاهر", color: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300", icon: Eye },
  hidden: { label: "مخفي", color: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300", icon: Eye },
  reported: { label: "مبلغ عنه", color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300", icon: AlertTriangle },
};

export default function ReviewsRatings() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [ratingFilter, setRatingFilter] = useState<string>("all");
  const [cityFilter, setCityFilter] = useState<string>("all");
  const [reviews, setReviews] = useState(mockReviews);
  const [replyText, setReplyText] = useState<{[key: string]: string}>({});
  const { toast } = useToast();

  const filteredReviews = reviews.filter(review => {
    const matchesSearch = 
      review.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.garageName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.serviceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.comment.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || review.status === statusFilter;
    const matchesRating = ratingFilter === "all" || 
      (ratingFilter === "negative" && review.isNegative) ||
      (ratingFilter === "positive" && !review.isNegative);
    const matchesCity = cityFilter === "all" || review.garageCity === cityFilter;
    
    return matchesSearch && matchesStatus && matchesRating && matchesCity;
  });

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating
            ? 'fill-yellow-400 text-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const getStatusBadge = (status: Review['status']) => {
    const config = statusConfig[status];
    const IconComponent = config.icon;
    return (
      <Badge className={`${config.color} flex items-center gap-1 px-3 py-1`}>
        <IconComponent className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const handleAction = (action: string, review: Review) => {
    toast({
      title: "تم تنفيذ الإجراء",
      description: `تم ${action} للمراجعة`,
    });
    
    if (action === "حذف") {
      setReviews(reviews.filter(r => r.id !== review.id));
    } else if (action === "إخفاء" || action === "إظهار") {
      setReviews(reviews.map(r => 
        r.id === review.id 
          ? { ...r, status: r.status === "hidden" ? "visible" : "hidden" as const }
          : r
      ));
    }
  };

  const handleReply = (reviewId: string) => {
    const reply = replyText[reviewId];
    if (reply?.trim()) {
      setReviews(reviews.map(r => 
        r.id === reviewId ? { ...r, adminReply: reply } : r
      ));
      setReplyText({ ...replyText, [reviewId]: "" });
      toast({
        title: "تم إرسال الرد",
        description: "تم إضافة ردك على المراجعة بنجاح",
      });
    }
  };

  const getReviewStats = () => {
    return {
      total: reviews.length,
      positive: reviews.filter(r => !r.isNegative).length,
      negative: reviews.filter(r => r.isNegative).length,
      reported: reviews.filter(r => r.status === "reported").length,
      avgRating: (reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1),
    };
  };

  const stats = getReviewStats();

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground font-arabic">المراجعات والتقييمات</h1>
          <p className="text-muted-foreground font-arabic">إدارة ومراقبة تقييمات المستخدمين</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200 dark:border-blue-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-700 dark:text-blue-300 font-arabic">إجمالي المراجعات</p>
                <p className="text-2xl font-bold text-blue-800 dark:text-blue-200">{stats.total}</p>
              </div>
              <MessageSquare className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 border-green-200 dark:border-green-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-700 dark:text-green-300 font-arabic">إيجابية</p>
                <p className="text-2xl font-bold text-green-800 dark:text-green-200">{stats.positive}</p>
              </div>
              <Star className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100/50 dark:from-red-900/20 dark:to-red-800/10 border-red-200 dark:border-red-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-red-700 dark:text-red-300 font-arabic">سلبية</p>
                <p className="text-2xl font-bold text-red-800 dark:text-red-200">{stats.negative}</p>
              </div>
              <ThumbsDown className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100/50 dark:from-orange-900/20 dark:to-orange-800/10 border-orange-200 dark:border-orange-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-orange-700 dark:text-orange-300 font-arabic">مبلغ عنها</p>
                <p className="text-2xl font-bold text-orange-800 dark:text-orange-200">{stats.reported}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-900/20 dark:to-purple-800/10 border-purple-200 dark:border-purple-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-700 dark:text-purple-300 font-arabic">متوسط التقييم</p>
                <p className="text-2xl font-bold text-purple-800 dark:text-purple-200">{stats.avgRating}</p>
              </div>
              <Star className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="البحث في المراجعات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 font-arabic"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="visible">ظاهرة</SelectItem>
                <SelectItem value="hidden">مخفية</SelectItem>
                <SelectItem value="reported">مبلغ عنها</SelectItem>
              </SelectContent>
            </Select>
            <Select value={ratingFilter} onValueChange={setRatingFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالتقييم" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع التقييمات</SelectItem>
                <SelectItem value="positive">إيجابية</SelectItem>
                <SelectItem value="negative">سلبية</SelectItem>
              </SelectContent>
            </Select>
            <Select value={cityFilter} onValueChange={setCityFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالمدينة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المدن</SelectItem>
                <SelectItem value="الرياض">الرياض</SelectItem>
                <SelectItem value="جدة">جدة</SelectItem>
                <SelectItem value="الدمام">الدمام</SelectItem>
                <SelectItem value="مكة">مكة</SelectItem>
                <SelectItem value="المدينة">المدينة</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all" className="font-arabic">جميع المراجعات</TabsTrigger>
          <TabsTrigger value="negative" className="font-arabic">المراجعات السلبية</TabsTrigger>
          <TabsTrigger value="reported" className="font-arabic">المبلغ عنها</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredReviews.map((review) => (
            <Card key={review.id} className={`${review.isNegative ? 'border-red-200 dark:border-red-800/30' : ''}`}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-4">
                    <div>
                      <p className="font-medium font-arabic">{review.customerName}</p>
                      <p className="text-sm text-muted-foreground font-arabic">{review.garageName} - {review.garageCity}</p>
                      <p className="text-xs text-muted-foreground font-arabic">{review.serviceName}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex">{renderStars(review.rating)}</div>
                    <span className="text-sm font-medium">{review.rating}</span>
                    {getStatusBadge(review.status)}
                  </div>
                </div>

                <p className="text-sm mb-4 font-arabic leading-relaxed">{review.comment}</p>

                {review.adminReply && (
                  <div className="bg-muted/30 p-4 rounded-lg mb-4">
                    <p className="text-sm font-medium mb-1 font-arabic">رد الإدارة:</p>
                    <p className="text-sm font-arabic">{review.adminReply}</p>
                  </div>
                )}

                {!review.adminReply && (
                  <div className="space-y-3">
                    <Textarea
                      placeholder="اكتب ردك على هذه المراجعة..."
                      value={replyText[review.id] || ""}
                      onChange={(e) => setReplyText({ ...replyText, [review.id]: e.target.value })}
                      className="font-arabic"
                    />
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <Button size="sm" onClick={() => handleReply(review.id)} disabled={!replyText[review.id]?.trim()}>
                          <Reply className="w-4 h-4 ml-2" />
                          رد
                        </Button>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="h-8 w-8 p-0" 
                          onClick={() => handleAction(review.status === "hidden" ? "إظهار" : "إخفاء", review)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive" 
                          onClick={() => handleAction("حذف", review)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex justify-between items-center text-xs text-muted-foreground mt-4">
                  <span className="font-arabic">{review.createdDate}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="negative">
          <div className="space-y-4">
            {filteredReviews.filter(r => r.isNegative).map((review) => (
              <Card key={review.id} className="border-red-200 dark:border-red-800/30">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center gap-4">
                      <div>
                        <p className="font-medium font-arabic">{review.customerName}</p>
                        <p className="text-sm text-muted-foreground font-arabic">{review.garageName} - {review.garageCity}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex">{renderStars(review.rating)}</div>
                      <Badge variant="destructive">{review.rating}</Badge>
                    </div>
                  </div>
                  <p className="text-sm mb-4 font-arabic">{review.comment}</p>
                  {review.adminReply && (
                    <div className="bg-muted/30 p-4 rounded-lg">
                      <p className="text-sm font-medium mb-1 font-arabic">رد الإدارة:</p>
                      <p className="text-sm font-arabic">{review.adminReply}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="reported">
          <div className="space-y-4">
            {filteredReviews.filter(r => r.status === "reported").map((review) => (
              <Card key={review.id} className="border-orange-200 dark:border-orange-800/30">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center gap-4">
                      <AlertTriangle className="w-8 h-8 text-orange-500" />
                      <div>
                        <p className="font-medium font-arabic">{review.customerName}</p>
                        <p className="text-sm text-muted-foreground font-arabic">{review.garageName}</p>
                        <Badge variant="outline" className="mt-1">مبلغ عنه</Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex">{renderStars(review.rating)}</div>
                    </div>
                  </div>
                  <p className="text-sm mb-4 font-arabic">{review.comment}</p>
                  <div className="flex gap-2">
                    <Button size="sm" variant="destructive" onClick={() => handleAction("حذف", review)}>
                      حذف المراجعة
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleAction("إخفاء", review)}>
                      إخفاء فقط
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}