import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Download,
  Eye,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  ShoppingBag,
  MapPin,
  Calendar,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  garageName: string;
  garageCity: string;
  services: string[];
  totalAmount: number;
  platformCommission: number;
  status: "pending" | "in_progress" | "completed" | "cancelled";
  createdDate: string;
  scheduledDate?: string;
  completedDate?: string;
  priority: "low" | "medium" | "high";
}

const mockOrders: Order[] = [
  {
    id: "1",
    orderNumber: "ORD-2024-001",
    customerName: "أحمد محمد الأحمد",
    customerPhone: "+966501234567",
    garageName: "كراج النجمة للصيانة",
    garageCity: "الرياض",
    services: ["تغيير الزيت", "فحص الفرامل"],
    totalAmount: 450,
    platformCommission: 45,
    status: "in_progress",
    createdDate: "2024-01-10",
    scheduledDate: "2024-01-15",
    priority: "high",
  },
  {
    id: "2",
    orderNumber: "ORD-2024-002",
    customerName: "فاطمة علي السالم",
    customerPhone: "+966509876543",
    garageName: "مركز الأمانة لخدمات السيارات",
    garageCity: "جدة",
    services: ["تبديل الإطارات", "فحص المحرك"],
    totalAmount: 800,
    platformCommission: 80,
    status: "completed",
    createdDate: "2024-01-08",
    scheduledDate: "2024-01-12",
    completedDate: "2024-01-12",
    priority: "medium",
  },
  {
    id: "3",
    orderNumber: "ORD-2024-003",
    customerName: "محمد سعد الغامدي",
    customerPhone: "+966505555555",
    garageName: "ورشة الإتقان للإصلاح",
    garageCity: "الدمام",
    services: ["صيانة شاملة"],
    totalAmount: 1200,
    platformCommission: 120,
    status: "pending",
    createdDate: "2024-01-11",
    scheduledDate: "2024-01-20",
    priority: "low",
  },
  {
    id: "4",
    orderNumber: "ORD-2024-004",
    customerName: "سارة أحمد الخليفة",
    customerPhone: "+966507777777",
    garageName: "مجمع السرعة للصيانة الشاملة",
    garageCity: "مكة",
    services: ["إصلاح التكييف", "تنظيف السيارة"],
    totalAmount: 650,
    platformCommission: 65,
    status: "cancelled",
    createdDate: "2024-01-09",
    priority: "medium",
  },
  {
    id: "5",
    orderNumber: "ORD-2024-005",
    customerName: "خالد عبدالله النعيمي",
    customerPhone: "+966503333333",
    garageName: "ورشة التميز للسيارات",
    garageCity: "المدينة",
    services: ["تبديل زيت ناقل الحركة", "فحص شامل"],
    totalAmount: 950,
    platformCommission: 95,
    status: "completed",
    createdDate: "2024-01-07",
    scheduledDate: "2024-01-10",
    completedDate: "2024-01-10",
    priority: "high",
  },
];

const statusConfig = {
  pending: { label: "في الانتظار", color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300", icon: Clock },
  in_progress: { label: "قيد التنفيذ", color: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300", icon: AlertCircle },
  completed: { label: "مكتمل", color: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300", icon: CheckCircle },
  cancelled: { label: "ملغي", color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300", icon: XCircle },
};

const priorityConfig = {
  low: { label: "منخفضة", color: "bg-gray-100 text-gray-600" },
  medium: { label: "متوسطة", color: "bg-yellow-100 text-yellow-700" },
  high: { label: "عالية", color: "bg-red-100 text-red-700" },
};

export default function OrdersOverview() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [cityFilter, setCityFilter] = useState<string>("all");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [activeTab, setActiveTab] = useState("all");
  const [orders] = useState(mockOrders);
  const { toast } = useToast();

  const filteredOrders = orders.filter(order => {
    const matchesSearch = 
      order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.garageName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customerPhone.includes(searchTerm);
    
    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    const matchesCity = cityFilter === "all" || order.garageCity === cityFilter;
    const matchesPriority = priorityFilter === "all" || order.priority === priorityFilter;
    const matchesTab = activeTab === "all" || order.status === activeTab;
    
    return matchesSearch && matchesStatus && matchesCity && matchesPriority && matchesTab;
  });

  const getStatusBadge = (status: Order['status']) => {
    const config = statusConfig[status];
    const IconComponent = config.icon;
    return (
      <Badge className={`${config.color} flex items-center gap-1 px-3 py-1`}>
        <IconComponent className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: Order['priority']) => {
    const config = priorityConfig[priority];
    return (
      <Badge variant="outline" className={`${config.color} text-xs`}>
        {config.label}
      </Badge>
    );
  };

  const handleAction = (action: string, order: Order) => {
    toast({
      title: "تم تنفيذ الإجراء",
      description: `تم ${action} للطلب ${order.orderNumber}`,
    });
  };

  const getOrderStats = () => {
    return {
      total: orders.length,
      pending: orders.filter(o => o.status === "pending").length,
      inProgress: orders.filter(o => o.status === "in_progress").length,
      completed: orders.filter(o => o.status === "completed").length,
      cancelled: orders.filter(o => o.status === "cancelled").length,
      totalRevenue: orders.reduce((sum, o) => sum + o.totalAmount, 0),
      totalCommission: orders.reduce((sum, o) => sum + o.platformCommission, 0),
    };
  };

  const stats = getOrderStats();

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">نظرة عامة على الطلبات</h1>
          <p className="text-muted-foreground">متابعة وإدارة جميع طلبات النظام</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 ml-2" />
            تصدير
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200 dark:border-blue-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-700 dark:text-blue-300">إجمالي الطلبات</p>
                <p className="text-2xl font-bold text-blue-800 dark:text-blue-200">{stats.total}</p>
              </div>
              <ShoppingBag className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100/50 dark:from-yellow-900/20 dark:to-yellow-800/10 border-yellow-200 dark:border-yellow-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-800 dark:text-yellow-200">{stats.pending}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200 dark:border-blue-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-700 dark:text-blue-300">قيد التنفيذ</p>
                <p className="text-2xl font-bold text-blue-800 dark:text-blue-200">{stats.inProgress}</p>
              </div>
              <AlertCircle className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 border-green-200 dark:border-green-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-700 dark:text-green-300">مكتملة</p>
                <p className="text-2xl font-bold text-green-800 dark:text-green-200">{stats.completed}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100/50 dark:from-red-900/20 dark:to-red-800/10 border-red-200 dark:border-red-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-red-700 dark:text-red-300">ملغية</p>
                <p className="text-2xl font-bold text-red-800 dark:text-red-200">{stats.cancelled}</p>
              </div>
              <XCircle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-900/20 dark:to-purple-800/10 border-purple-200 dark:border-purple-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-700 dark:text-purple-300">إجمالي الإيرادات</p>
                <p className="text-lg font-bold text-purple-800 dark:text-purple-200">{stats.totalRevenue.toLocaleString()} ر.س</p>
              </div>
              <Calendar className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100/50 dark:from-indigo-900/20 dark:to-indigo-800/10 border-indigo-200 dark:border-indigo-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-indigo-700 dark:text-indigo-300">عمولة المنصة</p>
                <p className="text-lg font-bold text-indigo-800 dark:text-indigo-200">{stats.totalCommission.toLocaleString()} ر.س</p>
              </div>
              <MapPin className="w-8 h-8 text-indigo-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="البحث بالاسم، رقم الطلب، الكراج..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="pending">في الانتظار</SelectItem>
                <SelectItem value="in_progress">قيد التنفيذ</SelectItem>
                <SelectItem value="completed">مكتمل</SelectItem>
                <SelectItem value="cancelled">ملغي</SelectItem>
              </SelectContent>
            </Select>
            <Select value={cityFilter} onValueChange={setCityFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالمدينة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المدن</SelectItem>
                <SelectItem value="الرياض">الرياض</SelectItem>
                <SelectItem value="جدة">جدة</SelectItem>
                <SelectItem value="الدمام">الدمام</SelectItem>
                <SelectItem value="مكة">مكة</SelectItem>
                <SelectItem value="المدينة">المدينة</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالأولوية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأولويات</SelectItem>
                <SelectItem value="high">عالية</SelectItem>
                <SelectItem value="medium">متوسطة</SelectItem>
                <SelectItem value="low">منخفضة</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="w-5 h-5" />
            قائمة الطلبات ({filteredOrders.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5 mb-6">
              <TabsTrigger value="all">الكل</TabsTrigger>
              <TabsTrigger value="pending">في الانتظار</TabsTrigger>
              <TabsTrigger value="in_progress">قيد التنفيذ</TabsTrigger>
              <TabsTrigger value="completed">مكتمل</TabsTrigger>
              <TabsTrigger value="cancelled">ملغي</TabsTrigger>
            </TabsList>
            
            <TabsContent value={activeTab} className="mt-0">
              <div className="rounded-lg border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-muted/50">
                      <TableHead className="text-right">رقم الطلب</TableHead>
                      <TableHead className="text-right">العميل</TableHead>
                      <TableHead className="text-right">الكراج</TableHead>
                      <TableHead className="text-right">الخدمات</TableHead>
                      <TableHead className="text-right">المبلغ</TableHead>
                      <TableHead className="text-right">العمولة</TableHead>
                      <TableHead className="text-right">التاريخ</TableHead>
                      <TableHead className="text-right">الحالة</TableHead>
                      <TableHead className="text-right">الأولوية</TableHead>
                      <TableHead className="text-right">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredOrders.map((order) => (
                      <TableRow key={order.id} className="hover:bg-muted/30 transition-colors">
                        <TableCell className="font-medium">{order.orderNumber}</TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{order.customerName}</p>
                            <p className="text-sm text-muted-foreground">{order.customerPhone}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{order.garageName}</p>
                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                              <MapPin className="w-3 h-3" />
                              {order.garageCity}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {order.services.slice(0, 2).map((service, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {service}
                              </Badge>
                            ))}
                            {order.services.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{order.services.length - 2}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">{order.totalAmount.toLocaleString()} ر.س</TableCell>
                        <TableCell className="font-medium text-green-600">{order.platformCommission.toLocaleString()} ر.س</TableCell>
                        <TableCell className="text-sm">
                          <div>
                            <p>إنشاء: {order.createdDate}</p>
                            {order.scheduledDate && <p className="text-muted-foreground">موعد: {order.scheduledDate}</p>}
                            {order.completedDate && <p className="text-green-600">اكتمل: {order.completedDate}</p>}
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(order.status)}</TableCell>
                        <TableCell>{getPriorityBadge(order.priority)}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button size="sm" variant="outline" className="h-8 w-8 p-0" onClick={() => handleAction('عرض التفاصيل', order)}>
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="outline" className="h-8 w-8 p-0" onClick={() => handleAction('تعديل الحالة', order)}>
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="outline" className="h-8 w-8 p-0 text-destructive hover:text-destructive" onClick={() => handleAction('حذف', order)}>
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}