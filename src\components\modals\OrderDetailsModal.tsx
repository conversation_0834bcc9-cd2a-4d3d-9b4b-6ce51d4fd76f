import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { Save, CreditCard, Bell, FileText, MessageSquare, Edit } from "lucide-react";

interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  vehicle: string;
  services: string[];
  expectedDate: string;
  status: 'new' | 'in_progress' | 'ready' | 'postponed';
  amount: number;
  notes: string;
}

interface OrderDetailsModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (updatedOrder: Order) => void;
}

export const OrderDetailsModal = ({ order, isOpen, onClose, onUpdate }: OrderDetailsModalProps) => {
  const [editedOrder, setEditedOrder] = useState<Order | null>(order);
  const [isEditing, setIsEditing] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState("");
  const [notificationMessage, setNotificationMessage] = useState("");
  const [workNotes, setWorkNotes] = useState("");
  const { toast } = useToast();

  if (!order || !editedOrder) return null;

  const handleSave = () => {
    onUpdate(editedOrder);
    setIsEditing(false);
    toast({
      title: "تم حفظ التغييرات",
      description: "تم تحديث بيانات الطلب بنجاح",
    });
  };

  const handlePaymentUpdate = () => {
    toast({
      title: "تم تحديث الدفع",
      description: `تم تسجيل دفعة بقيمة ${paymentAmount} ريال`,
    });
    setPaymentAmount("");
  };

  const handleSendNotification = () => {
    toast({
      title: "تم إرسال الإشعار",
      description: "تم إرسال إشعار للعميل بنجاح",
    });
    setNotificationMessage("");
  };

  const handleAddWorkNote = () => {
    toast({
      title: "تم حفظ الملاحظة",
      description: "تم إضافة ملاحظة العمل بنجاح",
    });
    setWorkNotes("");
  };

  const getStatusBadge = (status: Order['status']) => {
    const statusConfig = {
      new: { label: 'جديد', color: 'bg-blue-500/10 text-blue-700 border-blue-500/20' },
      in_progress: { label: 'قيد الإصلاح', color: 'bg-orange-500/10 text-orange-700 border-orange-500/20' },
      ready: { label: 'جاهز للاستلام', color: 'bg-green-500/10 text-green-700 border-green-500/20' },
      postponed: { label: 'مؤجل', color: 'bg-red-500/10 text-red-700 border-red-500/20' }
    };

    const config = statusConfig[status];
    return (
      <Badge className={`font-arabic ${config.color}`}>
        {config.label}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" dir="rtl">
        <DialogHeader>
          <DialogTitle className="font-arabic flex items-center justify-between">
            تفاصيل الطلب {order.orderNumber}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
              className="font-arabic"
            >
              <Edit className="h-4 w-4 ml-2" />
              {isEditing ? 'إلغاء التعديل' : 'تعديل'}
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium font-arabic block mb-2">اسم العميل</label>
              {isEditing ? (
                <Input
                  value={editedOrder.customerName}
                  onChange={(e) => setEditedOrder({...editedOrder, customerName: e.target.value})}
                  className="font-arabic"
                />
              ) : (
                <p className="font-arabic text-lg">{editedOrder.customerName}</p>
              )}
            </div>
            <div>
              <label className="text-sm font-medium font-arabic block mb-2">السيارة</label>
              {isEditing ? (
                <Input
                  value={editedOrder.vehicle}
                  onChange={(e) => setEditedOrder({...editedOrder, vehicle: e.target.value})}
                  className="font-arabic"
                />
              ) : (
                <p className="font-arabic text-lg">{editedOrder.vehicle}</p>
              )}
            </div>
            <div>
              <label className="text-sm font-medium font-arabic block mb-2">التاريخ المتوقع</label>
              {isEditing ? (
                <Input
                  type="date"
                  value={editedOrder.expectedDate}
                  onChange={(e) => setEditedOrder({...editedOrder, expectedDate: e.target.value})}
                />
              ) : (
                <p className="font-mono text-lg">{editedOrder.expectedDate}</p>
              )}
            </div>
            <div>
              <label className="text-sm font-medium font-arabic block mb-2">الحالة</label>
              {isEditing ? (
                <Select
                  value={editedOrder.status}
                  onValueChange={(value: Order['status']) => setEditedOrder({...editedOrder, status: value})}
                >
                  <SelectTrigger className="font-arabic">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new" className="font-arabic">جديد</SelectItem>
                    <SelectItem value="in_progress" className="font-arabic">قيد الإصلاح</SelectItem>
                    <SelectItem value="ready" className="font-arabic">جاهز للاستلام</SelectItem>
                    <SelectItem value="postponed" className="font-arabic">مؤجل</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div>{getStatusBadge(editedOrder.status)}</div>
              )}
            </div>
          </div>

          <div>
            <label className="text-sm font-medium font-arabic block mb-2">الخدمات المطلوبة</label>
            <div className="flex flex-wrap gap-2">
              {editedOrder.services.map((service, index) => (
                <Badge key={index} variant="outline" className="font-arabic">
                  {service}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <label className="text-sm font-medium font-arabic block mb-2">المبلغ الإجمالي</label>
            {isEditing ? (
              <Input
                type="number"
                value={editedOrder.amount}
                onChange={(e) => setEditedOrder({...editedOrder, amount: Number(e.target.value)})}
                className="font-mono"
              />
            ) : (
              <p className="font-mono text-lg font-bold text-primary">{editedOrder.amount} ريال</p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium font-arabic block mb-2">ملاحظات الطلب</label>
            {isEditing ? (
              <Textarea
                value={editedOrder.notes}
                onChange={(e) => setEditedOrder({...editedOrder, notes: e.target.value})}
                className="font-arabic"
                placeholder="أضف ملاحظات للطلب..."
              />
            ) : (
              <p className="font-arabic">{editedOrder.notes}</p>
            )}
          </div>

          {isEditing && (
            <div className="flex gap-2">
              <Button onClick={handleSave} className="font-arabic">
                <Save className="h-4 w-4 ml-2" />
                حفظ التغييرات
              </Button>
            </div>
          )}

          <Separator />

          {/* Action Sections */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Payment Section */}
            <div className="space-y-4">
              <h3 className="font-arabic font-semibold flex items-center">
                <CreditCard className="h-5 w-5 ml-2" />
                تحديث الدفع
              </h3>
              <div className="space-y-3">
                <Input
                  type="number"
                  placeholder="مبلغ الدفعة..."
                  value={paymentAmount}
                  onChange={(e) => setPaymentAmount(e.target.value)}
                  className="font-mono"
                />
                <Button 
                  onClick={handlePaymentUpdate}
                  variant="outline"
                  className="w-full font-arabic"
                  disabled={!paymentAmount}
                >
                  تسجيل دفعة
                </Button>
              </div>
            </div>

            {/* Notification Section */}
            <div className="space-y-4">
              <h3 className="font-arabic font-semibold flex items-center">
                <Bell className="h-5 w-5 ml-2" />
                إرسال إشعار للعميل
              </h3>
              <div className="space-y-3">
                <Textarea
                  placeholder="اكتب رسالة الإشعار..."
                  value={notificationMessage}
                  onChange={(e) => setNotificationMessage(e.target.value)}
                  className="font-arabic"
                />
                <Button 
                  onClick={handleSendNotification}
                  variant="outline"
                  className="w-full font-arabic"
                  disabled={!notificationMessage}
                >
                  إرسال إشعار
                </Button>
              </div>
            </div>
          </div>

          {/* Work Notes Section */}
          <div className="space-y-4">
            <h3 className="font-arabic font-semibold flex items-center">
              <MessageSquare className="h-5 w-5 ml-2" />
              ملاحظات العمل الداخلية
            </h3>
            <div className="space-y-3">
              <Textarea
                placeholder="أضف ملاحظة عمل جديدة..."
                value={workNotes}
                onChange={(e) => setWorkNotes(e.target.value)}
                className="font-arabic"
              />
              <Button 
                onClick={handleAddWorkNote}
                variant="outline"
                className="font-arabic"
                disabled={!workNotes}
              >
                إضافة ملاحظة
              </Button>
            </div>
            <div className="bg-muted/30 p-4 rounded-lg">
              <p className="text-sm text-muted-foreground font-arabic mb-2">آخر ملاحظات العمل:</p>
              <p className="font-arabic text-sm">
                • تم فحص المحرك - حالة جيدة<br/>
                • تم تغيير الزيت والفلتر<br/>
                • يحتاج تنظيف خارجي إضافي
              </p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" className="font-arabic">
              <FileText className="h-4 w-4 ml-2" />
              طباعة فاتورة
            </Button>
            <Button variant="outline" className="font-arabic">
              <FileText className="h-4 w-4 ml-2" />
              تقرير مفصل
            </Button>
            <Button variant="outline" className="font-arabic">
              مشاركة التحديث
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};