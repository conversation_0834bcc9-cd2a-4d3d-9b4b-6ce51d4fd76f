import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Save, Upload, X } from "lucide-react";

interface AddServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (service: {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: string;
    isActive: boolean;
    ordersCount: number;
    rating: number;
    hasImage: boolean;
  }) => void;
}

export const AddServiceModal = ({ isOpen, onClose, onAdd }: AddServiceModalProps) => {
  const [serviceName, setServiceName] = useState("");
  const [description, setDescription] = useState("");
  const [price, setPrice] = useState("");
  const [duration, setDuration] = useState("");
  const [isActive, setIsActive] = useState(true);
  const [hasImage, setHasImage] = useState(false);
  const { toast } = useToast();

  const handleSubmit = () => {
    if (!serviceName || !description || !price || !duration) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    const newService = {
      id: Date.now().toString(),
      name: serviceName,
      description,
      price: Number(price),
      duration,
      isActive,
      ordersCount: 0,
      rating: 0,
      hasImage
    };

    onAdd(newService);
    toast({
      title: "تم إضافة الخدمة",
      description: "تم إضافة الخدمة الجديدة بنجاح",
    });
    
    // Reset form
    setServiceName("");
    setDescription("");
    setPrice("");
    setDuration("");
    setIsActive(true);
    setHasImage(false);
    onClose();
  };

  const handleImageUpload = () => {
    // Simulate image upload
    setHasImage(true);
    toast({
      title: "تم رفع الصورة",
      description: "تم رفع صورة الخدمة بنجاح",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl" dir="rtl">
        <DialogHeader>
          <DialogTitle className="font-arabic">إضافة خدمة جديدة</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="serviceName" className="font-arabic">اسم الخدمة *</Label>
              <Input
                id="serviceName"
                value={serviceName}
                onChange={(e) => setServiceName(e.target.value)}
                placeholder="مثال: تغيير زيت المحرك"
                className="font-arabic"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="duration" className="font-arabic">المدة المتوقعة *</Label>
              <Input
                id="duration"
                value={duration}
                onChange={(e) => setDuration(e.target.value)}
                placeholder="مثال: 30 دقيقة"
                className="font-arabic"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="font-arabic">وصف الخدمة *</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="اكتب وصفاً مفصلاً للخدمة..."
              className="font-arabic"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="price" className="font-arabic">السعر (ريال) *</Label>
            <Input
              id="price"
              type="number"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              placeholder="0"
              className="font-mono"
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="isActive" className="font-arabic">تفعيل الخدمة</Label>
              <Switch
                id="isActive"
                checked={isActive}
                onCheckedChange={setIsActive}
              />
            </div>

            <div className="space-y-3">
              <Label className="font-arabic">صورة الخدمة</Label>
              <div className="flex items-center gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleImageUpload}
                  className="font-arabic"
                >
                  <Upload className="h-4 w-4 ml-2" />
                  رفع صورة
                </Button>
                {hasImage && (
                  <div className="flex items-center gap-2 text-green-600">
                    <span className="text-sm font-arabic">تم رفع الصورة</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setHasImage(false)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button onClick={handleSubmit} className="font-arabic">
              <Save className="h-4 w-4 ml-2" />
              إضافة الخدمة
            </Button>
            <Button variant="outline" onClick={onClose} className="font-arabic">
              إلغاء
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};