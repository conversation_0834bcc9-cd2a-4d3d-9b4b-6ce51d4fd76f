import { Outlet } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { GarageSidebar } from "./GarageSidebar";
import { GarageHeader } from "./GarageHeader";

export function GarageLayout() {
  return (
    <div dir="rtl" className="min-h-screen bg-background font-arabic">
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <GarageSidebar />
          <div className="flex-1 flex flex-col overflow-hidden">
            <GarageHeader />
            <main className="flex-1 overflow-auto bg-gradient-to-br from-background via-background/50 to-muted/20">
              <Outlet />
            </main>
          </div>
        </div>
      </SidebarProvider>
    </div>
  );
}