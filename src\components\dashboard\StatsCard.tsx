import { LucideIcon } from "lucide-react";
import { Card } from "@/components/ui/card";

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  change?: string;
  changeType?: "positive" | "negative" | "neutral";
  gradient?: "blue" | "green" | "orange" | "purple";
}

const gradientClasses = {
  blue: "from-blue-500/20 to-blue-600/5 border-blue-500/20",
  green: "from-green-500/20 to-green-600/5 border-green-500/20",
  orange: "from-orange-500/20 to-orange-600/5 border-orange-500/20",
  purple: "from-purple-500/20 to-purple-600/5 border-purple-500/20",
};

const iconBgClasses = {
  blue: "bg-blue-500/10 text-blue-600",
  green: "bg-green-500/10 text-green-600",
  orange: "bg-orange-500/10 text-orange-600",
  purple: "bg-purple-500/10 text-purple-600",
};

export function StatsCard({
  title,
  value,
  icon: Icon,
  change,
  changeType = "neutral",
  gradient = "blue",
}: StatsCardProps) {
  const getChangeColor = () => {
    switch (changeType) {
      case "positive":
        return "text-green-600";
      case "negative":
        return "text-red-500";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <Card className={`
      premium-card p-6 relative overflow-hidden
      bg-gradient-to-br ${gradientClasses[gradient]}
      hover:shadow-lg transition-all duration-300
      border backdrop-blur-sm
    `}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-current"></div>
        <div className="absolute -bottom-2 -left-2 w-16 h-16 rounded-full bg-current"></div>
      </div>

      <div className="relative">
        <div className="flex items-start justify-between mb-4">
          <div className={`
            w-12 h-12 rounded-xl flex items-center justify-center
            ${iconBgClasses[gradient]}
            backdrop-blur-sm shadow-sm
          `}>
            <Icon className="w-6 h-6" />
          </div>
          {change && (
            <div className={`text-sm font-medium ${getChangeColor()}`}>
              {change}
            </div>
          )}
        </div>

        <div className="space-y-1">
          <h3 className="text-sm font-arabic text-muted-foreground font-medium">
            {title}
          </h3>
          <p className="text-2xl font-almarai font-bold text-card-foreground">
            {value}
          </p>
        </div>
      </div>
    </Card>
  );
}