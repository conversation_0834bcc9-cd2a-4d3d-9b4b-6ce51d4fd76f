import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Search,
  Download,
  Eye,
  Ban,
  CheckCircle,
  Trash2,
  Building2,
  Star,
  MapPin,
  Phone,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Garage {
  id: string;
  name: string;
  ownerName: string;
  email: string;
  phone: string;
  city: string;
  address: string;
  status: "active" | "inactive" | "pending";
  registrationDate: string;
  totalOrders: number;
  rating: number;
  subscriptionType: "free" | "premium" | "enterprise";
  isVerified: boolean;
}

const mockGarages: Garage[] = [
  {
    id: "1",
    name: "كراج النجمة للصيانة",
    ownerName: "خالد أحمد المالكي",
    email: "<EMAIL>",
    phone: "+966501234567",
    city: "الرياض",
    address: "حي الملز، شارع الملك فهد",
    status: "active",
    registrationDate: "2024-01-10",
    totalOrders: 145,
    rating: 4.8,
    subscriptionType: "premium",
    isVerified: true,
  },
  {
    id: "2",
    name: "مركز الأمانة لخدمات السيارات",
    ownerName: "محمد عبدالله السعيد",
    email: "<EMAIL>",
    phone: "+966509876543",
    city: "جدة",
    address: "حي البلد، شارع التحلية",
    status: "active",
    registrationDate: "2024-01-05",
    totalOrders: 98,
    rating: 4.6,
    subscriptionType: "free",
    isVerified: true,
  },
  {
    id: "3",
    name: "ورشة الإتقان للإصلاح",
    ownerName: "أحمد سالم الغامدي",
    email: "<EMAIL>",
    phone: "+966505555555",
    city: "الدمام",
    address: "حي الشاطئ، الطريق الساحلي",
    status: "pending",
    registrationDate: "2024-01-15",
    totalOrders: 0,
    rating: 0,
    subscriptionType: "free",
    isVerified: false,
  },
  {
    id: "4",
    name: "مجمع السرعة للصيانة الشاملة",
    ownerName: "فهد عبدالرحمن القحطاني",
    email: "<EMAIL>",
    phone: "+966507777777",
    city: "مكة",
    address: "حي العزيزية، شارع إبراهيم الخليل",
    status: "inactive",
    registrationDate: "2023-12-20",
    totalOrders: 67,
    rating: 4.2,
    subscriptionType: "enterprise",
    isVerified: true,
  },
  {
    id: "5",
    name: "ورشة التميز للسيارات",
    ownerName: "سعد محمد النعيمي",
    email: "<EMAIL>",
    phone: "+966503333333",
    city: "المدينة",
    address: "حي العوالي، شارع سيد الشهداء",
    status: "active",
    registrationDate: "2023-11-30",
    totalOrders: 234,
    rating: 4.9,
    subscriptionType: "premium",
    isVerified: true,
  },
];

const statusConfig = {
  active: { label: "نشط", color: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300", icon: CheckCircle },
  inactive: { label: "غير نشط", color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300", icon: Ban },
  pending: { label: "في الانتظار", color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300", icon: Building2 },
};

const subscriptionConfig = {
  free: { label: "مجاني", color: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300" },
  premium: { label: "مميز", color: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300" },
  enterprise: { label: "مؤسسي", color: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300" },
};

export default function GaragesManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [cityFilter, setCityFilter] = useState<string>("all");
  const [subscriptionFilter, setSubscriptionFilter] = useState<string>("all");
  const [garages, setGarages] = useState(mockGarages);
  const { toast } = useToast();

  const filteredGarages = garages.filter(garage => {
    const matchesSearch = 
      garage.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      garage.ownerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      garage.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      garage.phone.includes(searchTerm);
    
    const matchesStatus = statusFilter === "all" || garage.status === statusFilter;
    const matchesCity = cityFilter === "all" || garage.city === cityFilter;
    const matchesSubscription = subscriptionFilter === "all" || garage.subscriptionType === subscriptionFilter;
    
    return matchesSearch && matchesStatus && matchesCity && matchesSubscription;
  });

  const getStatusBadge = (status: Garage['status']) => {
    const config = statusConfig[status];
    const IconComponent = config.icon;
    return (
      <Badge className={`${config.color} flex items-center gap-1 px-3 py-1`}>
        <IconComponent className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const getSubscriptionBadge = (type: Garage['subscriptionType']) => {
    const config = subscriptionConfig[type];
    return (
      <Badge className={`${config.color}`}>
        {config.label}
      </Badge>
    );
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-3 w-3 ${
          index < Math.floor(rating)
            ? 'fill-yellow-400 text-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const handleAction = (action: string, garage: Garage) => {
    toast({
      title: "تم تنفيذ الإجراء",
      description: `تم ${action} للكراج ${garage.name}`,
    });
    
    if (action === "تعطيل" || action === "تفعيل") {
      setGarages(garages.map(g => 
        g.id === garage.id 
          ? { ...g, status: g.status === "inactive" ? "active" : "inactive" as const }
          : g
      ));
    }
  };

  const getGarageStats = () => {
    return {
      total: garages.length,
      active: garages.filter(g => g.status === "active").length,
      inactive: garages.filter(g => g.status === "inactive").length,
      pending: garages.filter(g => g.status === "pending").length,
      verified: garages.filter(g => g.isVerified).length,
    };
  };

  const stats = getGarageStats();

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">إدارة الكراجات</h1>
          <p className="text-muted-foreground">إدارة أصحاب الكراجات ومقدمي الخدمات</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 ml-2" />
            تصدير
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200 dark:border-blue-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-700 dark:text-blue-300">إجمالي الكراجات</p>
                <p className="text-2xl font-bold text-blue-800 dark:text-blue-200">{stats.total}</p>
              </div>
              <Building2 className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 border-green-200 dark:border-green-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-700 dark:text-green-300">النشطة</p>
                <p className="text-2xl font-bold text-green-800 dark:text-green-200">{stats.active}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100/50 dark:from-red-900/20 dark:to-red-800/10 border-red-200 dark:border-red-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-red-700 dark:text-red-300">غير نشطة</p>
                <p className="text-2xl font-bold text-red-800 dark:text-red-200">{stats.inactive}</p>
              </div>
              <Ban className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100/50 dark:from-yellow-900/20 dark:to-yellow-800/10 border-yellow-200 dark:border-yellow-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-800 dark:text-yellow-200">{stats.pending}</p>
              </div>
              <Building2 className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-900/20 dark:to-purple-800/10 border-purple-200 dark:border-purple-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-700 dark:text-purple-300">موثقة</p>
                <p className="text-2xl font-bold text-purple-800 dark:text-purple-200">{stats.verified}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="البحث بالاسم، المالك، البريد الإلكتروني..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="active">نشط</SelectItem>
                <SelectItem value="inactive">غير نشط</SelectItem>
                <SelectItem value="pending">في الانتظار</SelectItem>
              </SelectContent>
            </Select>
            <Select value={cityFilter} onValueChange={setCityFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالمدينة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المدن</SelectItem>
                <SelectItem value="الرياض">الرياض</SelectItem>
                <SelectItem value="جدة">جدة</SelectItem>
                <SelectItem value="الدمام">الدمام</SelectItem>
                <SelectItem value="مكة">مكة</SelectItem>
                <SelectItem value="المدينة">المدينة</SelectItem>
              </SelectContent>
            </Select>
            <Select value={subscriptionFilter} onValueChange={setSubscriptionFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="نوع الاشتراك" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الاشتراكات</SelectItem>
                <SelectItem value="free">مجاني</SelectItem>
                <SelectItem value="premium">مميز</SelectItem>
                <SelectItem value="enterprise">مؤسسي</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Garages Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5" />
            قائمة الكراجات ({filteredGarages.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50">
                  <TableHead className="text-right">اسم الكراج</TableHead>
                  <TableHead className="text-right">المالك</TableHead>
                  <TableHead className="text-right">التواصل</TableHead>
                  <TableHead className="text-right">الموقع</TableHead>
                  <TableHead className="text-right">تاريخ التسجيل</TableHead>
                  <TableHead className="text-right">الطلبات</TableHead>
                  <TableHead className="text-right">التقييم</TableHead>
                  <TableHead className="text-right">الاشتراك</TableHead>
                  <TableHead className="text-right">الحالة</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredGarages.map((garage) => (
                  <TableRow key={garage.id} className="hover:bg-muted/30 transition-colors">
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="font-medium">{garage.name}</div>
                        {garage.isVerified && (
                          <Badge variant="outline" className="text-xs text-green-600 border-green-600">
                            موثق
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{garage.ownerName}</TableCell>
                    <TableCell>
                      <div>
                        <p className="text-sm">{garage.email}</p>
                        <p className="text-sm text-muted-foreground flex items-center gap-1">
                          <Phone className="w-3 h-3" />
                          {garage.phone}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="text-sm font-medium flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {garage.city}
                        </p>
                        <p className="text-xs text-muted-foreground">{garage.address}</p>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">{garage.registrationDate}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{garage.totalOrders} طلب</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <div className="flex">
                          {renderStars(garage.rating)}
                        </div>
                        <span className="text-sm font-medium">
                          {garage.rating > 0 ? garage.rating : "غير مقيم"}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{getSubscriptionBadge(garage.subscriptionType)}</TableCell>
                    <TableCell>{getStatusBadge(garage.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button size="sm" variant="outline" className="h-8 w-8 p-0" onClick={() => handleAction('عرض التفاصيل', garage)}>
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="h-8 w-8 p-0" 
                          onClick={() => handleAction(garage.status === "inactive" ? "تفعيل" : "تعطيل", garage)}
                        >
                          {garage.status === "inactive" ? <CheckCircle className="w-4 h-4" /> : <Ban className="w-4 h-4" />}
                        </Button>
                        <Button size="sm" variant="outline" className="h-8 w-8 p-0 text-destructive hover:text-destructive" onClick={() => handleAction('حذف', garage)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}