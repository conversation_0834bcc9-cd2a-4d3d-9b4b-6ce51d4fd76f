import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Eye, Search, Filter, Star, MessageSquare, Bell, Ban, Car, FileText } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Customer {
  id: string;
  name: string;
  phone: string;
  email: string;
  ordersCount: number;
  rating: number;
  vehicles: string[];
  lastOrderDate: string;
  totalSpent: number;
  isBlocked: boolean;
  avatarUrl?: string;
}

const mockCustomers: Customer[] = [
  {
    id: '1',
    name: 'أحمد محمد علي',
    phone: '+966501234567',
    email: '<EMAIL>',
    ordersCount: 12,
    rating: 4.8,
    vehicles: ['تويوتا كامري 2020', 'نيسان التيما 2018'],
    lastOrderDate: '2024-07-10',
    totalSpent: 3500,
    isBlocked: false,
    avatarUrl: '/api/placeholder/40/40'
  },
  {
    id: '2',
    name: 'فاطمة حسن',
    phone: '+966509876543',
    email: '<EMAIL>',
    ordersCount: 8,
    rating: 4.9,
    vehicles: ['هوندا أكورد 2019'],
    lastOrderDate: '2024-07-08',
    totalSpent: 2200,
    isBlocked: false
  },
  {
    id: '3',
    name: 'محمد سالم',
    phone: '+966555444333',
    email: '<EMAIL>',
    ordersCount: 15,
    rating: 4.6,
    vehicles: ['مرسيدس C-Class 2021', 'BMW X3 2020'],
    lastOrderDate: '2024-07-05',
    totalSpent: 5800,
    isBlocked: false
  },
  {
    id: '4',
    name: 'سارة أحمد',
    phone: '+966512345678',
    email: '<EMAIL>',
    ordersCount: 3,
    rating: 4.2,
    vehicles: ['تويوتا راف 4 2022'],
    lastOrderDate: '2024-06-28',
    totalSpent: 950,
    isBlocked: true
  }
];

const Customers = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterBy, setFilterBy] = useState("all");
  const [customers, setCustomers] = useState(mockCustomers);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const { toast } = useToast();

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.includes(searchTerm) || 
                         customer.phone.includes(searchTerm) ||
                         customer.email.includes(searchTerm);
    
    let matchesFilter = true;
    if (filterBy === "high_orders") matchesFilter = customer.ordersCount >= 10;
    if (filterBy === "high_rating") matchesFilter = customer.rating >= 4.5;
    if (filterBy === "blocked") matchesFilter = customer.isBlocked;
    if (filterBy === "active") matchesFilter = !customer.isBlocked;

    return matchesSearch && matchesFilter;
  });

  const handleViewDetails = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsDetailsModalOpen(true);
  };

  const handleToggleBlock = (customerId: string) => {
    setCustomers(customers.map(customer => 
      customer.id === customerId 
        ? { ...customer, isBlocked: !customer.isBlocked }
        : customer
    ));
    const customer = customers.find(c => c.id === customerId);
    toast({
      title: customer?.isBlocked ? "تم إلغاء الحظر" : "تم حظر العميل",
      description: `تم ${customer?.isBlocked ? 'إلغاء حظر' : 'حظر'} العميل بنجاح`,
    });
  };

  const handleSendMessage = (customer: Customer) => {
    toast({
      title: "إرسال رسالة",
      description: `تم فتح نافذة الرسائل للعميل ${customer.name}`,
    });
  };

  const handleSendNotification = (customer: Customer) => {
    toast({
      title: "إرسال إشعار",
      description: `تم إرسال إشعار للعميل ${customer.name}`,
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < Math.floor(rating)
            ? 'fill-yellow-400 text-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="flex-1 space-y-6 p-6" dir="rtl">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-foreground font-arabic">
          إدارة العملاء
        </h1>
        <p className="text-muted-foreground font-arabic">
          متابعة وإدارة جميع عملاء الكراج وبياناتهم
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-foreground">{customers.length}</p>
              <p className="text-sm text-muted-foreground font-arabic">إجمالي العملاء</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {customers.filter(c => !c.isBlocked).length}
              </p>
              <p className="text-sm text-muted-foreground font-arabic">عملاء نشطين</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {customers.reduce((acc, customer) => acc + customer.ordersCount, 0)}
              </p>
              <p className="text-sm text-muted-foreground font-arabic">إجمالي الطلبات</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">
                {(customers.reduce((acc, customer) => acc + customer.rating, 0) / customers.length).toFixed(1)}
              </p>
              <p className="text-sm text-muted-foreground font-arabic">متوسط التقييم</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">البحث والتصفية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="البحث عن عميل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10 font-arabic"
              />
            </div>
            <Select value={filterBy} onValueChange={setFilterBy}>
              <SelectTrigger className="w-full md:w-48 font-arabic">
                <Filter className="h-4 w-4 ml-2" />
                <SelectValue placeholder="تصفية حسب" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all" className="font-arabic">جميع العملاء</SelectItem>
                <SelectItem value="active" className="font-arabic">عملاء نشطين</SelectItem>
                <SelectItem value="blocked" className="font-arabic">عملاء محظورين</SelectItem>
                <SelectItem value="high_orders" className="font-arabic">طلبات كثيرة (+10)</SelectItem>
                <SelectItem value="high_rating" className="font-arabic">تقييم عالي (+4.5)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Customers Table */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">قائمة العملاء</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right font-arabic">العميل</TableHead>
                  <TableHead className="text-right font-arabic">رقم الهاتف</TableHead>
                  <TableHead className="text-right font-arabic">البريد الإلكتروني</TableHead>
                  <TableHead className="text-right font-arabic">عدد الطلبات</TableHead>
                  <TableHead className="text-right font-arabic">التقييم</TableHead>
                  <TableHead className="text-right font-arabic">إجمالي الإنفاق</TableHead>
                  <TableHead className="text-right font-arabic">الحالة</TableHead>
                  <TableHead className="text-right font-arabic">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCustomers.map((customer) => (
                  <TableRow key={customer.id} className="hover:bg-muted/50">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={customer.avatarUrl} />
                          <AvatarFallback className="font-arabic">
                            {customer.name.split(' ')[0].charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-arabic font-medium">{customer.name}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono">{customer.phone}</TableCell>
                    <TableCell className="font-mono">{customer.email}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="font-arabic">
                        {customer.ordersCount} طلب
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="flex">
                          {renderStars(customer.rating)}
                        </div>
                        <span className="text-sm font-medium">{customer.rating}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono">{customer.totalSpent} ريال</TableCell>
                    <TableCell>
                      {customer.isBlocked ? (
                        <Badge variant="destructive" className="font-arabic">محظور</Badge>
                      ) : (
                        <Badge variant="secondary" className="font-arabic">نشط</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleViewDetails(customer)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleSendMessage(customer)}
                        >
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleSendNotification(customer)}
                        >
                          <Bell className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className={`h-8 w-8 p-0 ${customer.isBlocked ? 'text-green-600' : 'text-red-600'}`}
                          onClick={() => handleToggleBlock(customer.id)}
                        >
                          <Ban className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Customer Details Modal */}
      <Dialog open={isDetailsModalOpen} onOpenChange={setIsDetailsModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto" dir="rtl">
          <DialogHeader>
            <DialogTitle className="font-arabic">تفاصيل العميل</DialogTitle>
          </DialogHeader>
          {selectedCustomer && (
            <div className="space-y-6">
              {/* Customer Info */}
              <div className="flex items-center gap-4 p-4 bg-muted/30 rounded-lg">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={selectedCustomer.avatarUrl} />
                  <AvatarFallback className="font-arabic text-xl">
                    {selectedCustomer.name.split(' ')[0].charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="text-xl font-bold font-arabic">{selectedCustomer.name}</h3>
                  <p className="text-muted-foreground font-mono">{selectedCustomer.phone}</p>
                  <p className="text-muted-foreground font-mono">{selectedCustomer.email}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <div className="flex">{renderStars(selectedCustomer.rating)}</div>
                    <span className="text-sm">{selectedCustomer.rating}</span>
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-primary">{selectedCustomer.totalSpent}</p>
                  <p className="text-sm text-muted-foreground font-arabic">ريال إجمالي</p>
                </div>
              </div>

              {/* Vehicles */}
              <div>
                <h4 className="font-arabic font-semibold mb-3 flex items-center gap-2">
                  <Car className="h-5 w-5" />
                  المركبات المسجلة
                </h4>
                <div className="grid gap-2">
                  {selectedCustomer.vehicles.map((vehicle, index) => (
                    <div key={index} className="p-3 bg-muted/30 rounded-lg">
                      <p className="font-arabic">{vehicle}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order History */}
              <div>
                <h4 className="font-arabic font-semibold mb-3 flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  سجل الطلبات
                </h4>
                <div className="space-y-2">
                  <div className="p-3 bg-muted/30 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="font-arabic">طلب #ORD-001 - تغيير زيت</span>
                      <Badge variant="secondary" className="font-arabic">مكتمل</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground font-mono">2024-07-10</p>
                  </div>
                  <div className="p-3 bg-muted/30 rounded-lg">
                    <div className="flex justify-between items-center">
                      <span className="font-arabic">طلب #ORD-002 - فحص شامل</span>
                      <Badge variant="outline" className="font-arabic">مكتمل</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground font-mono">2024-06-25</p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-4">
                <Button className="font-arabic">
                  <MessageSquare className="h-4 w-4 ml-2" />
                  إرسال رسالة
                </Button>
                <Button variant="outline" className="font-arabic">
                  <Bell className="h-4 w-4 ml-2" />
                  إرسال إشعار
                </Button>
                <Button 
                  variant={selectedCustomer.isBlocked ? "default" : "destructive"}
                  className="font-arabic"
                  onClick={() => handleToggleBlock(selectedCustomer.id)}
                >
                  <Ban className="h-4 w-4 ml-2" />
                  {selectedCustomer.isBlocked ? 'إلغاء الحظر' : 'حظر العميل'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Customers;