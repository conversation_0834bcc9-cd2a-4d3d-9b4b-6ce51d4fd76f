"use client";

import { useState, useCallback } from "react";
import { 
  Upload as UploadIcon, 
  Image as ImageIcon, 
  FileText, 
  Plus,
  Eye,
  Trash2,
  Download,
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface UploadedFile {
  id: string;
  file: File;
  preview: string;
  description: string;
  status: "new" | "uploading" | "uploaded" | "under-review" | "approved" | "rejected";
  uploadDate: Date;
}

interface PhotoSection {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  files: UploadedFile[];
  maxFiles?: number;
}

const statusColors = {
  new: "bg-primary/10 text-primary",
  uploading: "bg-yellow-500/10 text-yellow-600",
  uploaded: "bg-green-500/10 text-green-600",
  "under-review": "bg-orange-500/10 text-orange-600",
  approved: "bg-green-500/10 text-green-600",
  rejected: "bg-destructive/10 text-destructive",
};

const statusLabels = {
  new: "جديد",
  uploading: "جاري الرفع",
  uploaded: "مرفوع",
  "under-review": "قيد المراجعة",
  approved: "معتمد",
  rejected: "مرفوض",
};

export default function Upload() {
  const [sections, setSections] = useState<PhotoSection[]>([
    {
      id: "vehicle-photos",
      title: "صور المركبة",
      description: "صور خارجية وداخلية للمركبة",
      icon: ImageIcon,
      files: [],
      maxFiles: 10
    },
    {
      id: "damage-photos",
      title: "صور الأضرار",
      description: "صور الخدوش والحوادث أو الأضرار",
      icon: ImageIcon,
      files: [],
      maxFiles: 15
    },
    {
      id: "license-photos",
      title: "صور الترخيص",
      description: "صور وثيقة الترخيص الحالية",
      icon: FileText,
      files: [],
      maxFiles: 2
    },
    {
      id: "insurance-photos",
      title: "صور التأمين",
      description: "صور وثيقة التأمين فقط",
      icon: FileText,
      files: [],
      maxFiles: 2
    },
    {
      id: "other-photos",
      title: "صور أخرى",
      description: "فواتير ووثائق إضافية أو أي مرفقات مهمة",
      icon: FileText,
      files: []
    }
  ]);

  const [dragOver, setDragOver] = useState<string | null>(null);

  const handleFileUpload = useCallback((sectionId: string, files: FileList | null) => {
    if (!files) return;

    const newFiles: UploadedFile[] = Array.from(files).map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      preview: URL.createObjectURL(file),
      description: "",
      status: "new",
      uploadDate: new Date()
    }));

    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { ...section, files: [...section.files, ...newFiles] }
        : section
    ));
  }, []);

  const handleDrop = useCallback((e: React.DragEvent, sectionId: string) => {
    e.preventDefault();
    setDragOver(null);
    handleFileUpload(sectionId, e.dataTransfer.files);
  }, [handleFileUpload]);

  const handleDragOver = useCallback((e: React.DragEvent, sectionId: string) => {
    e.preventDefault();
    setDragOver(sectionId);
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragOver(null);
  }, []);

  const updateFileDescription = (sectionId: string, fileId: string, description: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            files: section.files.map(file => 
              file.id === fileId ? { ...file, description } : file
            )
          }
        : section
    ));
  };

  const deleteFile = (sectionId: string, fileId: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            files: section.files.filter(file => file.id !== fileId)
          }
        : section
    ));
  };

  const FileUploadArea = ({ section }: { section: PhotoSection }) => (
    <div
      className={`
        border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300
        ${dragOver === section.id 
          ? "border-primary bg-primary/5 scale-[1.02]" 
          : "border-border/50 hover:border-primary/50 hover:bg-muted/20"
        }
      `}
      onDrop={(e) => handleDrop(e, section.id)}
      onDragOver={(e) => handleDragOver(e, section.id)}
      onDragLeave={handleDragLeave}
    >
      <section.icon className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
      <h3 className="font-arabic font-medium text-lg mb-2">
        اسحب وأفلت الصور هنا
      </h3>
      <p className="text-muted-foreground font-arabic mb-4">
        أو انقر لاختيار الملفات
      </p>
      
      <input
        type="file"
        multiple
        accept="image/*"
        className="hidden"
        id={`upload-${section.id}`}
        onChange={(e) => handleFileUpload(section.id, e.target.files)}
      />
      
      <Button 
        asChild
        className="btn-premium text-white font-arabic gap-2"
      >
        <label htmlFor={`upload-${section.id}`} className="cursor-pointer">
          <Plus className="w-5 h-5" />
          إضافة صورة
        </label>
      </Button>
      
      {section.maxFiles && (
        <p className="text-xs text-muted-foreground mt-2 font-arabic">
          الحد الأقصى: {section.maxFiles} صور
        </p>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-background mobile-app-layout mobile-safe-area">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent p-4 md:p-6 border-b border-border/50">
        <div className="w-full">
          <div className="fade-in-up">
            <h1 className="text-3xl font-almarai font-bold text-foreground mb-2">
              رفع الصور
            </h1>
            <p className="text-muted-foreground font-arabic">
              ارفع صور مركبتك ووثائقك بسهولة وأمان
            </p>
          </div>
        </div>
      </div>

      <div className="w-full p-6 space-y-8">
        {sections.map((section, index) => (
          <Card 
            key={section.id}
            className="premium-card p-6 fade-in-up"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center">
                <section.icon className="w-6 h-6 text-primary" />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-almarai font-bold text-card-foreground">
                  {section.title}
                </h2>
                <p className="text-muted-foreground font-arabic">
                  {section.description}
                </p>
              </div>
              <Badge variant="outline" className="font-arabic">
                {section.files.length} صورة
              </Badge>
            </div>

            {/* Upload Area */}
            <FileUploadArea section={section} />

            {/* Uploaded Files */}
            {section.files.length > 0 && (
              <div className="mt-6">
                <h3 className="font-arabic font-medium text-lg mb-4">
                  الصور المرفوعة ({section.files.length})
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {section.files.map((file) => (
                    <Card key={file.id} className="overflow-hidden">
                      <div className="relative group">
                        <img
                          src={file.preview}
                          alt={file.description || "صورة مرفوعة"}
                          className="w-full h-48 object-cover"
                        />
                        
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                size="sm"
                                variant="secondary"
                                className="w-8 h-8 p-0"
                              >
                                <Eye className="w-4 h-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl">
                              <DialogHeader>
                                <DialogTitle className="font-arabic">
                                  معاينة الصورة
                                </DialogTitle>
                              </DialogHeader>
                              <img
                                src={file.preview}
                                alt={file.description}
                                className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
                              />
                            </DialogContent>
                          </Dialog>
                          
                          <Button
                            size="sm"
                            variant="secondary"
                            className="w-8 h-8 p-0"
                            onClick={() => deleteFile(section.id, file.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                        
                        <div className="absolute top-2 left-2">
                          <Badge className={statusColors[file.status]}>
                            {statusLabels[file.status]}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="p-4">
                        <div className="mb-3">
                          <Label htmlFor={`desc-${file.id}`} className="font-arabic">
                            وصف الصورة
                          </Label>
                          <Textarea
                            id={`desc-${file.id}`}
                            placeholder="أضف وصفاً للصورة..."
                            value={file.description}
                            onChange={(e) => updateFileDescription(section.id, file.id, e.target.value)}
                            className="mt-1 h-20 font-arabic"
                          />
                        </div>
                        
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span className="font-arabic">
                            {file.uploadDate.toLocaleDateString('ar-SA')}
                          </span>
                          <span>
                            {(file.file.size / 1024 / 1024).toFixed(1)} MB
                          </span>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </Card>
        ))}

        {/* Action Buttons */}
        <Card className="premium-card p-6">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="btn-premium text-white font-arabic gap-2" 
              size="lg"
              onClick={() => alert('سيتم رفع جميع الصور إلى الخادم')}
            >
              <UploadIcon className="w-5 h-5" />
              رفع جميع الصور
            </Button>
            <Button 
              variant="outline" 
              size="lg" 
              className="font-arabic gap-2"
              onClick={() => alert('سيتم تحميل جميع الصور كملف مضغوط')}
            >
              <Download className="w-5 h-5" />
              تحميل كملف مضغوط
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}