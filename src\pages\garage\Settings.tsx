import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { 
  Save, 
  Upload, 
  MapPin, 
  Eye, 
  EyeOff, 
  Shield, 
  LogOut, 
  UserX,
  Moon,
  Sun,
  Globe
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Settings = () => {
  const [garageName, setGarageName] = useState("كراج الأمانة للخدمات السريعة");
  const [garagePhone, setGaragePhone] = useState("+966501234567");
  const [garageEmail, setGarageEmail] = useState("<EMAIL>");
  const [garageAddress, setGarageAddress] = useState("شارع الملك فهد، الرياض");
  const [workingHours, setWorkingHours] = useState("8:00 ص - 10:00 م");
  const [garageDescription, setGarageDescription] = useState("نحن نقدم أفضل خدمات الصيانة والإصلاح للسيارات بجودة عالية وأسعار منافسة");
  
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [autoNotifications, setAutoNotifications] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  const { toast } = useToast();

  const handleSaveGarageInfo = () => {
    toast({
      title: "تم حفظ المعلومات",
      description: "تم تحديث معلومات الكراج بنجاح",
    });
  };

  const handleChangePassword = () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع حقول كلمة المرور",
        variant: "destructive",
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      toast({
        title: "خطأ",
        description: "كلمة المرور الجديدة وتأكيدها غير متطابقتين",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "تم تغيير كلمة المرور",
      description: "تم تغيير كلمة المرور بنجاح",
    });

    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
  };

  const handleUploadLogo = () => {
    toast({
      title: "رفع الشعار",
      description: "تم رفع شعار الكراج بنجاح",
    });
  };

  const handleTwoFactorToggle = (enabled: boolean) => {
    setTwoFactorEnabled(enabled);
    toast({
      title: enabled ? "تم تفعيل المصادقة الثنائية" : "تم إلغاء المصادقة الثنائية",
      description: enabled ? "تم تفعيل المصادقة الثنائية لحسابك" : "تم إلغاء المصادقة الثنائية",
    });
  };

  const handleDeactivateAccount = () => {
    toast({
      title: "تعطيل الحساب",
      description: "تم فتح نافذة تعطيل الحساب",
      variant: "destructive",
    });
  };

  const handleLogout = () => {
    toast({
      title: "تسجيل الخروج",
      description: "تم تسجيل الخروج بنجاح",
    });
  };

  return (
    <div className="flex-1 space-y-6 p-6" dir="rtl">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-foreground font-arabic">
          إعدادات الكراج
        </h1>
        <p className="text-muted-foreground font-arabic">
          إدارة معلومات الكراج وإعدادات الحساب
        </p>
      </div>

      {/* Garage Information */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            معلومات الكراج
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Logo Section */}
          <div className="flex items-center gap-6">
            <Avatar className="h-24 w-24">
              <AvatarImage src="/api/placeholder/96/96" />
              <AvatarFallback className="font-arabic text-xl bg-primary/10">
                كراج
              </AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <Label className="font-arabic">شعار الكراج</Label>
              <Button onClick={handleUploadLogo} variant="outline" className="font-arabic">
                <Upload className="h-4 w-4 ml-2" />
                رفع شعار جديد
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="garageName" className="font-arabic">اسم الكراج</Label>
              <Input
                id="garageName"
                value={garageName}
                onChange={(e) => setGarageName(e.target.value)}
                className="font-arabic"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="garagePhone" className="font-arabic">رقم الهاتف</Label>
              <Input
                id="garagePhone"
                value={garagePhone}
                onChange={(e) => setGaragePhone(e.target.value)}
                className="font-mono"
                dir="ltr"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="garageEmail" className="font-arabic">البريد الإلكتروني</Label>
              <Input
                id="garageEmail"
                type="email"
                value={garageEmail}
                onChange={(e) => setGarageEmail(e.target.value)}
                className="font-mono"
                dir="ltr"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="workingHours" className="font-arabic">ساعات العمل</Label>
              <Input
                id="workingHours"
                value={workingHours}
                onChange={(e) => setWorkingHours(e.target.value)}
                className="font-arabic"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="garageAddress" className="font-arabic">العنوان</Label>
            <Input
              id="garageAddress"
              value={garageAddress}
              onChange={(e) => setGarageAddress(e.target.value)}
              className="font-arabic"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="garageDescription" className="font-arabic">وصف الكراج</Label>
            <Textarea
              id="garageDescription"
              value={garageDescription}
              onChange={(e) => setGarageDescription(e.target.value)}
              className="font-arabic"
              rows={3}
            />
          </div>

          <Button onClick={handleSaveGarageInfo} className="font-arabic">
            <Save className="h-4 w-4 ml-2" />
            حفظ معلومات الكراج
          </Button>
        </CardContent>
      </Card>

      {/* Account Settings */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic flex items-center gap-2">
            <Shield className="h-5 w-5" />
            إعدادات الحساب
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Password Change */}
          <div className="space-y-4">
            <h3 className="font-arabic font-semibold">تغيير كلمة المرور</h3>
            
            <div className="space-y-2">
              <Label htmlFor="currentPassword" className="font-arabic">كلمة المرور الحالية</Label>
              <div className="relative">
                <Input
                  id="currentPassword"
                  type={showCurrentPassword ? "text" : "password"}
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  className="pl-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute left-0 top-0 h-full px-3"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="newPassword" className="font-arabic">كلمة المرور الجديدة</Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    type={showNewPassword ? "text" : "password"}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="pl-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-0 top-0 h-full px-3"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="font-arabic">تأكيد كلمة المرور</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="pl-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute left-0 top-0 h-full px-3"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </div>

            <Button onClick={handleChangePassword} variant="outline" className="font-arabic">
              <Save className="h-4 w-4 ml-2" />
              تغيير كلمة المرور
            </Button>
          </div>

          <Separator />

          {/* Two-Factor Authentication */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-arabic font-medium">المصادقة الثنائية</Label>
              <p className="text-sm text-muted-foreground font-arabic">
                إضافة طبقة حماية إضافية لحسابك
              </p>
            </div>
            <Switch
              checked={twoFactorEnabled}
              onCheckedChange={handleTwoFactorToggle}
            />
          </div>

          <Separator />

          {/* Login Info */}
          <div className="space-y-2">
            <Label className="font-arabic font-medium">معلومات تسجيل الدخول</Label>
            <p className="text-sm text-muted-foreground font-arabic">
              آخر تسجيل دخول: اليوم 2:30 م
            </p>
            <p className="text-sm text-muted-foreground font-arabic">
              الجهاز: Chrome على Windows
            </p>
          </div>
        </CardContent>
      </Card>

      {/* System Settings */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic flex items-center gap-2">
            <Globe className="h-5 w-5" />
            إعدادات النظام
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-arabic font-medium">الوضع المظلم</Label>
              <p className="text-sm text-muted-foreground font-arabic">
                تبديل بين الوضع المظلم والفاتح
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Sun className="h-4 w-4" />
              <Switch
                checked={isDarkMode}
                onCheckedChange={setIsDarkMode}
              />
              <Moon className="h-4 w-4" />
            </div>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-arabic font-medium">الإشعارات التلقائية</Label>
              <p className="text-sm text-muted-foreground font-arabic">
                إرسال إشعارات تلقائية للعملاء عند تحديث الطلبات
              </p>
            </div>
            <Switch
              checked={autoNotifications}
              onCheckedChange={setAutoNotifications}
            />
          </div>

          <Separator />

          <div className="space-y-2">
            <Label className="font-arabic font-medium">اللغة</Label>
            <div className="flex items-center gap-2">
              <span className="text-sm font-arabic">العربية</span>
              <span className="text-xs text-muted-foreground font-arabic">(افتراضي)</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Management */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic flex items-center gap-2">
            <UserX className="h-5 w-5" />
            إدارة الحساب
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <Button 
              onClick={handleDeactivateAccount}
              variant="destructive" 
              className="font-arabic"
            >
              <UserX className="h-4 w-4 ml-2" />
              تعطيل الحساب
            </Button>
            
            <Button 
              onClick={handleLogout}
              variant="outline" 
              className="font-arabic"
            >
              <LogOut className="h-4 w-4 ml-2" />
              تسجيل الخروج
            </Button>
          </div>
          
          <div className="text-sm text-muted-foreground font-arabic">
            <p>تعطيل الحساب سيمنع الوصول إلى النظام مؤقتاً ويمكن إعادة تفعيله لاحقاً.</p>
            <p>تسجيل الخروج سيقوم بإنهاء الجلسة الحالية فقط.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Settings;