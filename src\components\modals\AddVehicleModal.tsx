import { useState } from "react";
import { Plus, Car, FileText, Calendar } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";

interface AddVehicleModalProps {
  children: React.ReactNode;
}

export function AddVehicleModal({ children }: AddVehicleModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    model: "",
    year: "",
    chassisNumber: "",
    licensePlate: "",
    color: "",
    mileage: "",
    licenseExpiry: "",
    insuranceExpiry: "",
    notes: ""
  });
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "تمت إضافة المركبة بنجاح",
      description: `تم إضافة ${formData.name || 'المركبة الجديدة'} إلى قائمة مركباتك`,
    });
    setIsOpen(false);
    setFormData({
      name: "",
      model: "",
      year: "",
      chassisNumber: "",
      licensePlate: "",
      color: "",
      mileage: "",
      licenseExpiry: "",
      insuranceExpiry: "",
      notes: ""
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-almarai flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
              <Car className="w-4 h-4 text-primary-foreground" />
            </div>
            إضافة مركبة جديدة
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card className="premium-card p-4">
            <h3 className="font-arabic font-semibold mb-4 flex items-center gap-2">
              <Car className="w-4 h-4 text-primary" />
              معلومات المركبة الأساسية
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name" className="font-arabic">اسم المركبة</Label>
                <Input
                  id="name"
                  placeholder="مثال: سيارتي الأساسية"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="font-arabic"
                />
              </div>
              
              <div>
                <Label htmlFor="model" className="font-arabic">الموديل والسنة</Label>
                <Input
                  id="model"
                  placeholder="مثال: تويوتا كامري 2022"
                  value={formData.model}
                  onChange={(e) => setFormData({...formData, model: e.target.value})}
                  className="font-arabic"
                />
              </div>
              
              <div>
                <Label htmlFor="chassisNumber" className="font-arabic">رقم الشاصي</Label>
                <Input
                  id="chassisNumber"
                  placeholder="مثال: JTDKN3DU8E0123456"
                  value={formData.chassisNumber}
                  onChange={(e) => setFormData({...formData, chassisNumber: e.target.value})}
                  className="font-mono"
                />
              </div>
              
              <div>
                <Label htmlFor="licensePlate" className="font-arabic">رقم اللوحة</Label>
                <Input
                  id="licensePlate"
                  placeholder="مثال: أ ب ج 1234"
                  value={formData.licensePlate}
                  onChange={(e) => setFormData({...formData, licensePlate: e.target.value})}
                  className="font-arabic"
                />
              </div>
              
              <div>
                <Label htmlFor="color" className="font-arabic">اللون</Label>
                <Input
                  id="color"
                  placeholder="مثال: أبيض لؤلؤي"
                  value={formData.color}
                  onChange={(e) => setFormData({...formData, color: e.target.value})}
                  className="font-arabic"
                />
              </div>
              
              <div>
                <Label htmlFor="mileage" className="font-arabic">المسافة المقطوعة</Label>
                <Input
                  id="mileage"
                  placeholder="مثال: 45000"
                  value={formData.mileage}
                  onChange={(e) => setFormData({...formData, mileage: e.target.value})}
                  className="font-arabic"
                />
              </div>
            </div>
          </Card>

          <Card className="premium-card p-4">
            <h3 className="font-arabic font-semibold mb-4 flex items-center gap-2">
              <Calendar className="w-4 h-4 text-primary" />
              تواريخ مهمة
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="licenseExpiry" className="font-arabic">تاريخ انتهاء الترخيص</Label>
                <Input
                  id="licenseExpiry"
                  type="date"
                  value={formData.licenseExpiry}
                  onChange={(e) => setFormData({...formData, licenseExpiry: e.target.value})}
                  className="font-arabic"
                />
              </div>
              
              <div>
                <Label htmlFor="insuranceExpiry" className="font-arabic">تاريخ انتهاء التأمين</Label>
                <Input
                  id="insuranceExpiry"
                  type="date"
                  value={formData.insuranceExpiry}
                  onChange={(e) => setFormData({...formData, insuranceExpiry: e.target.value})}
                  className="font-arabic"
                />
              </div>
            </div>
          </Card>

          <Card className="premium-card p-4">
            <h3 className="font-arabic font-semibold mb-4 flex items-center gap-2">
              <FileText className="w-4 h-4 text-primary" />
              ملاحظات إضافية
            </h3>
            
            <Textarea
              placeholder="أي ملاحظات أو معلومات إضافية عن المركبة..."
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              className="font-arabic h-24"
            />
          </Card>

          <div className="flex gap-3 pt-4">
            <Button 
              type="submit" 
              className="flex-1 btn-premium text-white font-arabic gap-2"
            >
              <Plus className="w-4 h-4" />
              إضافة المركبة
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsOpen(false)}
              className="font-arabic"
            >
              إلغاء
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}