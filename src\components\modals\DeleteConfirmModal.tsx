import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { AlertTriangle, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface DeleteConfirmModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onConfirm?: () => void;
  title: string;
  description: string;
  children?: React.ReactNode;
}

export const DeleteConfirmModal = ({ isOpen, onClose, onConfirm, title, description, children }: DeleteConfirmModalProps) => {
  const { toast } = useToast();

  // If controlled mode (with isOpen prop)
  if (isOpen !== undefined) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md" dir="rtl">
          <DialogHeader>
            <DialogTitle className="font-arabic flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              {title}
            </DialogTitle>
          </DialogHeader>
          
          <div className="py-4">
            <p className="font-arabic text-muted-foreground">{description}</p>
          </div>

          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={onClose} className="font-arabic">
              إلغاء
            </Button>
            <Button variant="destructive" onClick={onConfirm} className="font-arabic">
              حذف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  // Legacy trigger mode (with children)
  const handleDelete = () => {
    toast({
      title: "تم الحذف بنجاح",
      description: "تم حذف العنصر المحدد",
      variant: "destructive",
    });
    onConfirm?.();
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-3 font-almarai text-destructive">
            <div className="w-10 h-10 bg-destructive/10 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-destructive" />
            </div>
            {title}
          </AlertDialogTitle>
          <AlertDialogDescription className="font-arabic text-base leading-relaxed">
            {description}
            <br />
            <span className="text-destructive font-medium">هذا الإجراء لا يمكن التراجع عنه.</span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-3">
          <AlertDialogCancel className="font-arabic">إلغاء</AlertDialogCancel>
          <AlertDialogAction 
            onClick={handleDelete}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 font-arabic gap-2"
          >
            <Trash2 className="w-4 h-4" />
            تأكيد الحذف
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};