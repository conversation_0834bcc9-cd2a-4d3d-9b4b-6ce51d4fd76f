import { useState, useCallback } from "react";
import { Camera, Upload, X, Eye } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

interface PhotoUploadModalProps {
  children: React.ReactNode;
}

interface UploadedPhoto {
  id: string;
  file: File;
  preview: string;
  description: string;
}

export function PhotoUploadModal({ children }: PhotoUploadModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [photos, setPhotos] = useState<UploadedPhoto[]>([]);
  const [dragOver, setDragOver] = useState(false);
  const { toast } = useToast();

  const handleFileUpload = useCallback((files: FileList | null) => {
    if (!files) return;

    const newPhotos: UploadedPhoto[] = Array.from(files).map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      preview: URL.createObjectURL(file),
      description: ""
    }));

    setPhotos(prev => [...prev, ...newPhotos]);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileUpload(e.dataTransfer.files);
  }, [handleFileUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const removePhoto = (id: string) => {
    setPhotos(prev => prev.filter(photo => photo.id !== id));
  };

  const updateDescription = (id: string, description: string) => {
    setPhotos(prev => prev.map(photo => 
      photo.id === id ? { ...photo, description } : photo
    ));
  };

  const handleSubmit = () => {
    toast({
      title: "تم رفع الصور بنجاح",
      description: `تم رفع ${photos.length} صورة`,
    });
    setPhotos([]);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-almarai flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
              <Camera className="w-4 h-4 text-primary-foreground" />
            </div>
            إضافة صور
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Upload Area */}
          <div
            className={`
              border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300
              ${dragOver 
                ? "border-primary bg-primary/5 scale-[1.02]" 
                : "border-border/50 hover:border-primary/50 hover:bg-muted/20"
              }
            `}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={() => setDragOver(false)}
          >
            <Camera className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-arabic font-medium text-lg mb-2">
              اسحب وأفلت الصور هنا
            </h3>
            <p className="text-muted-foreground font-arabic mb-4">
              أو انقر لاختيار الملفات
            </p>
            
            <input
              type="file"
              multiple
              accept="image/*"
              className="hidden"
              id="photo-upload"
              onChange={(e) => handleFileUpload(e.target.files)}
            />
            
            <Button asChild className="btn-premium text-white font-arabic gap-2">
              <label htmlFor="photo-upload" className="cursor-pointer">
                <Upload className="w-5 h-5" />
                اختيار الصور
              </label>
            </Button>
          </div>

          {/* Uploaded Photos */}
          {photos.length > 0 && (
            <div>
              <h3 className="font-arabic font-medium text-lg mb-4">
                الصور المحددة ({photos.length})
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {photos.map((photo) => (
                  <div key={photo.id} className="border border-border rounded-lg overflow-hidden">
                    <div className="relative group">
                      <img
                        src={photo.preview}
                        alt="صورة مرفوعة"
                        className="w-full h-48 object-cover"
                      />
                      
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          className="w-8 h-8 p-0"
                          onClick={() => window.open(photo.preview)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          className="w-8 h-8 p-0"
                          onClick={() => removePhoto(photo.id)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="p-4">
                      <Label className="font-arabic">وصف الصورة</Label>
                      <Textarea
                        placeholder="أضف وصفاً للصورة..."
                        value={photo.description}
                        onChange={(e) => updateDescription(photo.id, e.target.value)}
                        className="mt-1 h-20 font-arabic"
                      />
                      <div className="mt-2 text-xs text-muted-foreground">
                        {(photo.file.size / 1024 / 1024).toFixed(1)} MB
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <Button 
              onClick={handleSubmit}
              disabled={photos.length === 0}
              className="flex-1 btn-premium text-white font-arabic gap-2"
            >
              <Upload className="w-4 h-4" />
              رفع الصور ({photos.length})
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setIsOpen(false)}
              className="font-arabic"
            >
              إلغاء
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}