"use client";

import React from "react";
import {
  Car,
  CreditCard,
  DollarSign,
  Wrench,
  Calendar,
  BarChart3,
  <PERSON><PERSON>hart,
  LogIn,
  TrendingUp,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
  Zap,
  Target,
  Award,
  Star
} from "lucide-react";
import { StatsCard } from "@/components/dashboard/StatsCard";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { RecentActivity } from "@/components/dashboard/RecentActivity";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import { XAxis, YAxis, CartesianGrid, ResponsiveContainer, AreaChart, Area, PieChart as RechartsPieChart, Cell, Pie, BarChart, Bar } from "recharts";

// Chart Data
const monthlyExpenses = [
  { month: "يناير", amount: 1200, services: 3 },
  { month: "فبراير", amount: 1800, services: 5 },
  { month: "مارس", amount: 2200, services: 7 },
  { month: "أبريل", amount: 1600, services: 4 },
  { month: "مايو", amount: 2800, services: 8 },
  { month: "يونيو", amount: 2500, services: 6 }
];

const expenseDistribution = [
  { name: "صيانة دورية", value: 45, color: "#6366F1", amount: 1125 },
  { name: "إصلاحات", value: 33, color: "#8B5CF6", amount: 825 },
  { name: "قطع غيار", value: 22, color: "#EC4899", amount: 550 }
];

const serviceTypes = [
  { name: "تغيير زيت", count: 12, color: "#10B981" },
  { name: "فحص شامل", count: 8, color: "#F59E0B" },
  { name: "إصلاح فرامل", count: 6, color: "#EF4444" },
  { name: "تبديل إطارات", count: 4, color: "#6366F1" }
];

interface DashboardProps {
  initialData?: {
    user: {
      name: string;
      email: string;
    };
    stats: {
      totalVehicles: number;
      pendingServices: number;
      completedServices: number;
      totalSpent: number;
    };
    recentActivity: Array<{
      id: number;
      type: string;
      message: string;
      date: string;
      status: string;
    }>;
  };
}

export default function Dashboard({ initialData }: DashboardProps) {
  const router = useRouter();

  // Use server-side data if available, otherwise fall back to default values
  const userData = initialData?.user || { name: "أحمد محمد", email: "<EMAIL>" };
  const statsData = initialData?.stats || {
    totalVehicles: 3,
    pendingServices: 2,
    completedServices: 15,
    totalSpent: 2500
  };

  return (
    <div className="flex-1 min-h-screen bg-gradient-to-br from-background via-background to-muted/5" dir="rtl">
      <div className="w-full max-w-none">
        {/* Mobile App Header */}
        <div className="fade-in-up relative">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-primary/5 to-accent/10 rounded-b-[2rem] md:rounded-b-[3rem] -z-10" />
          <div className="px-4 pt-6 pb-8 md:p-8">
            {/* Mobile Header */}
            <div className="flex items-center justify-between mb-6 md:hidden">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-primary via-primary-light to-primary-dark flex items-center justify-center shadow-xl">
                  <Car className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-almarai font-bold text-foreground">
                    مرحباً، {userData.name}
                  </h1>
                  <p className="text-sm text-muted-foreground font-arabic">
                    {new Date().toLocaleDateString('ar-SA')}
                  </p>
                </div>
              </div>
              <Button
                onClick={() => router.push('/auth')}
                size="sm"
                className="bg-gradient-to-r from-primary to-primary-dark text-white border-0 shadow-lg rounded-xl px-4"
              >
                <LogIn className="w-4 h-4" />
              </Button>
            </div>

            {/* Desktop Header */}
            <div className="hidden md:flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-6">
                  <div className="w-20 h-20 rounded-3xl bg-gradient-to-br from-primary via-primary-light to-primary-dark flex items-center justify-center shadow-2xl">
                    <Car className="w-10 h-10 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl lg:text-5xl font-almarai font-bold bg-gradient-to-r from-foreground via-primary to-primary-dark bg-clip-text text-transparent">
                      مرحباً، {userData.name}
                    </h1>
                    <p className="text-lg text-muted-foreground font-arabic mt-2">
                      إليك نظرة عامة على خدمات السيارات والصيانة
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                <div className="flex items-center gap-3 px-6 py-3 rounded-2xl bg-gradient-to-r from-muted/50 to-muted/30 backdrop-blur-sm border border-border/50">
                  <Calendar className="w-5 h-5 text-primary" />
                  <div className="text-sm">
                    <div className="font-medium text-foreground">اليوم</div>
                    <div className="text-muted-foreground">{new Date().toLocaleDateString('ar-SA')}</div>
                  </div>
                </div>
                <Button
                  onClick={() => router.push('/auth')}
                  className="bg-gradient-to-r from-primary via-primary-light to-primary-dark hover:from-primary-dark hover:to-primary text-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300 px-8 py-4 rounded-2xl text-lg"
                >
                  <LogIn className="w-5 h-5 mr-2" />
                  تسجيل الدخول
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile-First Stats Grid */}
        <div className="px-4 md:px-8 -mt-4 md:-mt-8 relative z-10">
          {/* Mobile Stats - 2x2 Grid */}
          <div className="grid grid-cols-2 gap-3 md:hidden">
            <div className="fade-in-up" style={{ animationDelay: "0.1s" }}>
              <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-indigo-500/10 via-purple-500/5 to-pink-500/10 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-transparent to-purple-500/20 opacity-50" />
                <CardContent className="relative p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                      <Car className="w-5 h-5 text-white" />
                    </div>
                    <Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-md text-xs px-2 py-1">
                      +12%
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-xs font-arabic font-medium text-muted-foreground">عدد المركبات</h3>
                    <div className="text-2xl font-bold text-foreground font-almarai">{statsData.totalVehicles}</div>
                    <p className="text-xs text-muted-foreground font-arabic">+1 هذا الشهر</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="fade-in-up" style={{ animationDelay: "0.2s" }}>
              <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-emerald-500/10 via-teal-500/5 to-cyan-500/10 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 via-transparent to-teal-500/20 opacity-50" />
                <CardContent className="relative p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-lg">
                      <CreditCard className="w-5 h-5 text-white" />
                    </div>
                    <Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-md text-xs px-2 py-1">
                      +15%
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-xs font-arabic font-medium text-muted-foreground">إجمالي الدفعات</h3>
                    <div className="text-xl font-bold text-foreground font-almarai">{(statsData.totalSpent/1000).toFixed(1)}k</div>
                    <p className="text-xs text-muted-foreground font-arabic">ريال سعودي</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="fade-in-up" style={{ animationDelay: "0.3s" }}>
              <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/20 via-transparent to-orange-500/20 opacity-50" />
                <CardContent className="relative p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center shadow-lg">
                      <DollarSign className="w-5 h-5 text-white" />
                    </div>
                    <Badge variant="destructive" className="bg-gradient-to-r from-red-500 to-pink-600 text-white border-0 shadow-md text-xs px-2 py-1">
                      -8%
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-xs font-arabic font-medium text-muted-foreground">الرصيد المتبقي</h3>
                    <div className="text-2xl font-bold text-foreground font-almarai">2.3k</div>
                    <p className="text-xs text-muted-foreground font-arabic">ريال سعودي</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="fade-in-up" style={{ animationDelay: "0.4s" }}>
              <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-violet-500/10 via-purple-500/5 to-fuchsia-500/10 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 via-transparent to-purple-500/20 opacity-50" />
                <CardContent className="relative p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center shadow-lg">
                      <Calendar className="w-5 h-5 text-white" />
                    </div>
                    <Badge className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-md text-xs px-2 py-1">
                      قريباً
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <h3 className="text-xs font-arabic font-medium text-muted-foreground">المواعيد القادمة</h3>
                    <div className="text-2xl font-bold text-foreground font-almarai">{statsData.pendingServices}</div>
                    <p className="text-xs text-muted-foreground font-arabic">خلال الأسبوع</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Desktop Stats - 4 Column Grid */}
          <div className="hidden md:grid grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="fade-in-up" style={{ animationDelay: "0.1s" }}>
              <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-indigo-500/10 via-purple-500/5 to-pink-500/10 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-transparent to-purple-500/20 opacity-50" />
                <CardContent className="relative p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-xl">
                      <Car className="w-7 h-7 text-white" />
                    </div>
                    <Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-md">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      +12%
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-sm font-arabic font-medium text-muted-foreground">عدد المركبات</h3>
                    <div className="text-3xl font-bold text-foreground font-almarai">{statsData.totalVehicles}</div>
                    <p className="text-xs text-muted-foreground font-arabic">+1 هذا الشهر</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="fade-in-up" style={{ animationDelay: "0.2s" }}>
              <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-emerald-500/10 via-teal-500/5 to-cyan-500/10 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 via-transparent to-teal-500/20 opacity-50" />
                <CardContent className="relative p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-xl">
                      <CreditCard className="w-7 h-7 text-white" />
                    </div>
                    <Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-md">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      +15%
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-sm font-arabic font-medium text-muted-foreground">إجمالي الدفعات</h3>
                    <div className="text-3xl font-bold text-foreground font-almarai">{statsData.totalSpent.toLocaleString()}</div>
                    <p className="text-xs text-muted-foreground font-arabic">ريال سعودي</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="fade-in-up" style={{ animationDelay: "0.3s" }}>
              <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-amber-500/20 via-transparent to-orange-500/20 opacity-50" />
                <CardContent className="relative p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center shadow-xl">
                      <DollarSign className="w-7 h-7 text-white" />
                    </div>
                    <Badge variant="destructive" className="bg-gradient-to-r from-red-500 to-pink-600 text-white border-0 shadow-md">
                      <TrendingUp className="w-3 h-3 mr-1 rotate-180" />
                      -8%
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-sm font-arabic font-medium text-muted-foreground">الرصيد المتبقي</h3>
                    <div className="text-3xl font-bold text-foreground font-almarai">2,350</div>
                    <p className="text-xs text-muted-foreground font-arabic">ريال سعودي</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="fade-in-up" style={{ animationDelay: "0.4s" }}>
              <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-violet-500/10 via-purple-500/5 to-fuchsia-500/10 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 via-transparent to-purple-500/20 opacity-50" />
                <CardContent className="relative p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center shadow-xl">
                      <Calendar className="w-7 h-7 text-white" />
                    </div>
                    <Badge className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-0 shadow-md">
                      <Clock className="w-3 h-3 mr-1" />
                      قريباً
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-sm font-arabic font-medium text-muted-foreground">المواعيد القادمة</h3>
                    <div className="text-3xl font-bold text-foreground font-almarai">{statsData.pendingServices}</div>
                    <p className="text-xs text-muted-foreground font-arabic">خلال الأسبوع القادم</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Main Content - Mobile App Style */}
        <div className="px-4 md:px-8 space-y-6 md:space-y-8">
          {/* Quick Actions */}
          <div className="fade-in-up" style={{ animationDelay: "0.5s" }}>
            <QuickActions />
          </div>

          {/* Mobile Charts Layout */}
          <div className="md:hidden space-y-6">
            {/* Mobile Service Types */}
            <div className="fade-in-up" style={{ animationDelay: "0.6s" }}>
              <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-transparent to-teal-500/5 opacity-50" />
                <CardHeader className="relative pb-3">
                  <CardTitle className="font-almarai flex items-center gap-3 text-lg">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-lg">
                      <Activity className="w-5 h-5 text-white" />
                    </div>
                    الخدمات الأكثر طلباً
                  </CardTitle>
                </CardHeader>
                <CardContent className="relative">
                  <div className="space-y-3">
                    {serviceTypes.slice(0, 3).map((service, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-muted/20 to-muted/10 border border-border/30">
                        <div className="flex items-center gap-3">
                          <div
                            className="w-8 h-8 rounded-xl flex items-center justify-center shadow-md"
                            style={{ background: service.color }}
                          >
                            <span className="text-sm font-bold text-white">{index + 1}</span>
                          </div>
                          <span className="font-arabic font-medium text-foreground">{service.name}</span>
                        </div>
                        <Badge
                          className="text-white border-0 shadow-md font-bold px-2 py-1"
                          style={{ background: service.color }}
                        >
                          {service.count}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Mobile Expense Distribution */}
            <div className="fade-in-up" style={{ animationDelay: "0.7s" }}>
              <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 via-transparent to-purple-500/5 opacity-50" />
                <CardHeader className="relative pb-3">
                  <CardTitle className="font-almarai flex items-center gap-3 text-lg">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center shadow-lg">
                      <PieChart className="w-5 h-5 text-white" />
                    </div>
                    توزيع المصروفات
                  </CardTitle>
                </CardHeader>
                <CardContent className="relative">
                  <div className="space-y-3">
                    {expenseDistribution.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-muted/20 to-muted/10">
                        <div className="flex items-center gap-3">
                          <div
                            className="w-4 h-4 rounded-full shadow-md"
                            style={{ backgroundColor: item.color }}
                          />
                          <span className="text-sm font-arabic font-medium text-foreground">{item.name}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-bold text-foreground">{item.value}%</div>
                          <div className="text-xs text-muted-foreground">{item.amount} ر.س</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Left Column - Charts & Analytics */}
            <div className="xl:col-span-2 space-y-8">

            {/* Monthly Expenses Chart */}
            <div className="fade-in-up" style={{ animationDelay: "0.6s" }}>
              <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-50" />
                <CardHeader className="relative pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="font-almarai flex items-center gap-3 text-xl">
                      <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-xl">
                        <BarChart3 className="w-6 h-6 text-white" />
                      </div>
                      تطور المصروفات الشهرية
                    </CardTitle>
                    <Badge className="gap-1 bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-md px-3 py-1">
                      <TrendingUp className="w-4 h-4" />
                      +18% هذا الشهر
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="relative">
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={monthlyExpenses} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                        <defs>
                          <linearGradient id="expenseGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="0%" stopColor="#6366F1" stopOpacity={0.4} />
                            <stop offset="50%" stopColor="#8B5CF6" stopOpacity={0.2} />
                            <stop offset="100%" stopColor="#A855F7" stopOpacity={0.05} />
                          </linearGradient>
                          <linearGradient id="strokeGradient" x1="0" y1="0" x2="1" y2="0">
                            <stop offset="0%" stopColor="#6366F1" />
                            <stop offset="50%" stopColor="#8B5CF6" />
                            <stop offset="100%" stopColor="#A855F7" />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="2 4" stroke="hsl(var(--border))" strokeOpacity={0.2} />
                        <XAxis
                          dataKey="month"
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                        />
                        <YAxis
                          axisLine={false}
                          tickLine={false}
                          tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                          tickFormatter={(value) => `${value.toLocaleString()}`}
                        />
                        <ChartTooltip
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div className="bg-card/95 backdrop-blur-sm border border-border/50 rounded-xl p-4 shadow-2xl">
                                  <p className="font-arabic font-semibold text-foreground mb-2">{label}</p>
                                  <div className="space-y-1">
                                    <div className="flex items-center gap-2">
                                      <div className="w-3 h-3 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600"></div>
                                      <span className="text-sm text-muted-foreground">المبلغ:</span>
                                      <span className="font-bold text-foreground">{payload[0].value?.toLocaleString()} ر.س</span>
                                    </div>
                                  </div>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="amount"
                          stroke="url(#strokeGradient)"
                          strokeWidth={3}
                          fill="url(#expenseGradient)"
                          dot={{ fill: '#6366F1', strokeWidth: 2, r: 4 }}
                          activeDot={{ r: 6, stroke: '#6366F1', strokeWidth: 2, fill: '#ffffff' }}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Service Types Chart */}
            <div className="fade-in-up" style={{ animationDelay: "0.7s" }}>
              <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm">
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-transparent to-teal-500/5 opacity-50" />
                <CardHeader className="relative pb-4">
                  <CardTitle className="font-almarai flex items-center gap-3 text-xl">
                    <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-xl">
                      <Activity className="w-6 h-6 text-white" />
                    </div>
                    أنواع الخدمات الأكثر طلباً
                  </CardTitle>
                </CardHeader>
                <CardContent className="relative">
                  <div className="space-y-4">
                    {serviceTypes.map((service, index) => (
                      <div key={index} className="group flex items-center justify-between p-4 rounded-2xl bg-gradient-to-r from-muted/20 to-muted/10 hover:from-muted/40 hover:to-muted/20 transition-all duration-300 border border-border/30 hover:border-border/50 hover:shadow-lg">
                        <div className="flex items-center gap-4">
                          <div
                            className="w-12 h-12 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300"
                            style={{ background: `linear-gradient(135deg, ${service.color}, ${service.color}dd)` }}
                          >
                            <span className="text-lg font-bold text-white">{index + 1}</span>
                          </div>
                          <span className="font-arabic font-semibold text-foreground text-lg">{service.name}</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="w-32 bg-muted/50 rounded-full h-3 overflow-hidden">
                            <div
                              className="h-3 rounded-full transition-all duration-1000 ease-out"
                              style={{
                                width: `${(service.count / 12) * 100}%`,
                                background: `linear-gradient(90deg, ${service.color}, ${service.color}dd)`
                              }}
                            />
                          </div>
                          <Badge
                            className="min-w-[60px] justify-center text-white border-0 shadow-lg font-bold px-3 py-1"
                            style={{ background: service.color }}
                          >
                            {service.count}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
            </div>

            {/* Right Column - Recent Activity & Pie Chart */}
            <div className="space-y-8">
              {/* Expense Distribution Pie Chart */}
              <div className="fade-in-up" style={{ animationDelay: "0.8s" }}>
                <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm">
                  <div className="absolute inset-0 bg-gradient-to-br from-pink-500/5 via-transparent to-purple-500/5 opacity-50" />
                  <CardHeader className="relative pb-4">
                    <CardTitle className="font-almarai flex items-center gap-3 text-lg">
                      <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center shadow-lg">
                        <PieChart className="w-5 h-5 text-white" />
                      </div>
                      توزيع المصروفات
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="relative">
                    <div className="h-[250px] flex items-center justify-center mb-4 relative">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <defs>
                            <filter id="pieGlow">
                              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                              <feMerge>
                                <feMergeNode in="coloredBlur"/>
                                <feMergeNode in="SourceGraphic"/>
                              </feMerge>
                            </filter>
                          </defs>
                          <Pie
                            data={expenseDistribution}
                            cx="50%"
                            cy="50%"
                            innerRadius={50}
                            outerRadius={90}
                            dataKey="value"
                            strokeWidth={2}
                            stroke="#ffffff"
                            filter="url(#pieGlow)"
                          >
                            {expenseDistribution.map((entry, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={entry.color}
                                style={{ cursor: 'pointer' }}
                              />
                            ))}
                          </Pie>
                          <ChartTooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                const data = payload[0].payload;
                                return (
                                  <div className="bg-card/95 backdrop-blur-sm border border-border/50 rounded-xl p-3 shadow-2xl">
                                    <div className="flex items-center gap-2 mb-2">
                                      <div
                                        className="w-3 h-3 rounded-full"
                                        style={{ backgroundColor: data.color }}
                                      />
                                      <span className="font-arabic font-semibold text-foreground">{data.name}</span>
                                    </div>
                                    <div className="text-sm text-muted-foreground">
                                      النسبة: <span className="font-bold text-foreground">{data.value}%</span><br/>
                                      المبلغ: <span className="font-bold text-foreground">{data.amount} ر.س</span>
                                    </div>
                                  </div>
                                );
                              }
                              return null;
                            }}
                          />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                      {/* Center Label */}
                      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-foreground font-almarai">2,500</div>
                          <div className="text-sm text-muted-foreground font-arabic">ر.س</div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      {expenseDistribution.map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-muted/20 to-muted/10 hover:from-muted/40 hover:to-muted/20 transition-all duration-300">
                          <div className="flex items-center gap-3">
                            <div
                              className="w-4 h-4 rounded-full shadow-md"
                              style={{ backgroundColor: item.color }}
                            />
                            <span className="text-sm font-arabic font-medium text-foreground">{item.name}</span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold text-foreground">{item.value}%</div>
                            <div className="text-xs text-muted-foreground">{item.amount} ر.س</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Activity */}
              <div className="fade-in-up" style={{ animationDelay: "0.9s" }}>
                <RecentActivity />
              </div>
            </div>
          </div>

        {/* Latest Status Card */}
        <div className="fade-in-up" style={{ animationDelay: "1.0s" }}>
          <Card className="relative overflow-hidden border-0 shadow-2xl bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-transparent to-teal-500/10 opacity-50" />
            <CardContent className="relative p-8">
              <div className="flex items-start gap-6">
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center shadow-2xl">
                    <Wrench className="w-8 h-8 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-white" />
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div>
                    <h3 className="font-almarai font-bold text-foreground text-2xl mb-2 flex items-center gap-3">
                      آخر حالة إصلاح
                      <Badge className="bg-gradient-to-r from-emerald-500 to-teal-600 text-white border-0 shadow-md">
                        <Star className="w-3 h-3 mr-1" />
                        مكتملة
                      </Badge>
                    </h3>
                    <p className="text-muted-foreground font-arabic text-lg leading-relaxed">
                      تم إنجاز الصيانة الدورية لسيارة تويوتا كامري (أ ب ج 1234) بنجاح.
                      تم تغيير الزيت والفلاتر وفحص الفرامل.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-emerald-500/10 to-emerald-500/5">
                      <div className="w-8 h-8 rounded-lg bg-emerald-500/20 flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-emerald-600" />
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground font-arabic">الحالة</div>
                        <div className="font-bold text-emerald-600">مكتملة</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-500/5">
                      <div className="w-8 h-8 rounded-lg bg-blue-500/20 flex items-center justify-center">
                        <DollarSign className="w-4 h-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground font-arabic">التكلفة</div>
                        <div className="font-bold text-blue-600">850 ريال</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 rounded-xl bg-gradient-to-r from-purple-500/10 to-purple-500/5">
                      <div className="w-8 h-8 rounded-lg bg-purple-500/20 flex items-center justify-center">
                        <Calendar className="w-4 h-4 text-purple-600" />
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground font-arabic">التاريخ</div>
                        <div className="font-bold text-purple-600">15 نوفمبر</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Mobile Recent Activity */}
        <div className="md:hidden fade-in-up" style={{ animationDelay: "0.8s" }}>
          <RecentActivity />
        </div>
        </div>
      </div>
    </div>
  );
}