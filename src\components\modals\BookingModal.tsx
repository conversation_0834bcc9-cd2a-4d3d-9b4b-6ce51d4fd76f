"use client";

import { useState } from "react";
import { Calendar, Car, Phone, MessageCircle } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";

interface BookingModalProps {
  children: React.ReactNode;
  garageName?: string;
}

export function BookingModal({ children, garageName = "الكراج المحدد" }: BookingModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState("");
  const [selectedTime, setSelectedTime] = useState("");
  const [selectedVehicle, setSelectedVehicle] = useState("");
  const [serviceType, setServiceType] = useState("");
  const [description, setDescription] = useState("");
  const [contactMethod, setContactMethod] = useState("phone");
  const { toast } = useToast();

  const availableTimes = [
    "08:00", "09:00", "10:00", "11:00", "12:00", 
    "13:00", "14:00", "15:00", "16:00", "17:00"
  ];

  const vehicles = [
    "تويوتا كامري 2022 - أ ب ج 1234",
    "هوندا أكورد 2021 - د هـ و 5678",
    "نيسان ألتيما 2020 - ز ح ط 9012"
  ];

  const serviceTypes = [
    "صيانة دورية",
    "تغيير زيت",
    "إصلاح فرامل",
    "فحص شامل",
    "إصلاح طوارئ",
    "خدمة أخرى"
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "تم حجز الموعد بنجاح",
      description: `موعدك في ${garageName} يوم ${selectedDate} الساعة ${selectedTime}`,
    });
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-almarai flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-primary-foreground" />
            </div>
            حجز موعد صيانة
          </DialogTitle>
          <p className="text-muted-foreground font-arabic">
            حجز موعد في {garageName}
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card className="premium-card p-4">
            <h3 className="font-arabic font-semibold mb-4 flex items-center gap-2">
              <Car className="w-4 h-4 text-primary" />
              اختيار المركبة والخدمة
            </h3>
            
            <div className="space-y-4">
              <div>
                <Label className="font-arabic">المركبة</Label>
                <select 
                  className="w-full mt-1 p-3 border border-border rounded-lg bg-background font-arabic"
                  value={selectedVehicle}
                  onChange={(e) => setSelectedVehicle(e.target.value)}
                >
                  <option value="">اختر المركبة</option>
                  {vehicles.map((vehicle, index) => (
                    <option key={index} value={vehicle}>{vehicle}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <Label className="font-arabic">نوع الخدمة</Label>
                <select 
                  className="w-full mt-1 p-3 border border-border rounded-lg bg-background font-arabic"
                  value={serviceType}
                  onChange={(e) => setServiceType(e.target.value)}
                >
                  <option value="">اختر نوع الخدمة</option>
                  {serviceTypes.map((service, index) => (
                    <option key={index} value={service}>{service}</option>
                  ))}
                </select>
              </div>
            </div>
          </Card>

          <Card className="premium-card p-4">
            <h3 className="font-arabic font-semibold mb-4 flex items-center gap-2">
              <Calendar className="w-4 h-4 text-primary" />
              اختيار التاريخ والوقت
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="font-arabic">التاريخ</Label>
                <Input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className="font-arabic"
                />
              </div>
              
              <div>
                <Label className="font-arabic">الوقت المفضل</Label>
                <div className="grid grid-cols-3 gap-2 mt-2">
                  {availableTimes.map((time) => (
                    <button
                      key={time}
                      type="button"
                      onClick={() => setSelectedTime(time)}
                      className={`p-2 text-sm rounded-lg border transition-colors font-mono ${
                        selectedTime === time
                          ? "bg-primary text-primary-foreground border-primary"
                          : "bg-background hover:bg-muted border-border"
                      }`}
                    >
                      {time}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          <Card className="premium-card p-4">
            <h3 className="font-arabic font-semibold mb-4 flex items-center gap-2">
              <MessageCircle className="w-4 h-4 text-primary" />
              تفاصيل إضافية
            </h3>
            
            <div className="space-y-4">
              <div>
                <Label className="font-arabic">وصف المشكلة أو الخدمة المطلوبة</Label>
                <Textarea
                  placeholder="اشرح بالتفصيل ما تحتاجه..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="font-arabic h-24"
                />
              </div>
              
              <div>
                <Label className="font-arabic">طريقة التواصل المفضلة</Label>
                <div className="flex gap-3 mt-2">
                  <button
                    type="button"
                    onClick={() => setContactMethod("phone")}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors font-arabic ${
                      contactMethod === "phone"
                        ? "bg-primary text-primary-foreground border-primary"
                        : "bg-background hover:bg-muted border-border"
                    }`}
                  >
                    <Phone className="w-4 h-4" />
                    اتصال هاتفي
                  </button>
                  <button
                    type="button"
                    onClick={() => setContactMethod("whatsapp")}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors font-arabic ${
                      contactMethod === "whatsapp"
                        ? "bg-primary text-primary-foreground border-primary"
                        : "bg-background hover:bg-muted border-border"
                    }`}
                  >
                    <MessageCircle className="w-4 h-4" />
                    واتساب
                  </button>
                </div>
              </div>
            </div>
          </Card>

          <div className="bg-muted/30 rounded-lg p-4">
            <h4 className="font-arabic font-medium mb-2">ملخص الحجز</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">الكراج:</span>
                <span className="font-medium">{garageName}</span>
              </div>
              {selectedVehicle && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">المركبة:</span>
                  <span className="font-medium">{selectedVehicle}</span>
                </div>
              )}
              {serviceType && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">الخدمة:</span>
                  <span className="font-medium">{serviceType}</span>
                </div>
              )}
              {selectedDate && selectedTime && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">الموعد:</span>
                  <span className="font-medium">{selectedDate} - {selectedTime}</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button 
              type="submit" 
              className="flex-1 btn-premium text-white font-arabic gap-2"
            >
              <Calendar className="w-4 h-4" />
              تأكيد الحجز
            </Button>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsOpen(false)}
              className="font-arabic"
            >
              إلغاء
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}