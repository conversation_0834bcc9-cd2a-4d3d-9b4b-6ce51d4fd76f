"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowRight,
  Star,
  MapPin,
  Clock,
  Phone,
  MessageCircle,
  Share2,
  Heart,
  Calendar,
  ChevronLeft,
  ChevronRight,
  ThumbsUp,
  ThumbsDown,
  Camera,
  Wrench,
  DollarSign
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { BookingModal } from "@/components/modals/BookingModal";
import { PhotoUploadModal } from "@/components/modals/PhotoUploadModal";
import { useToast } from "@/hooks/use-toast";

// Mock data - in real app this would come from API
const garageData = {
  id: 1,
  name: "كراج الخليج المتطور",
  rating: 4.8,
  reviews: 245,
  location: "الرياض، حي النخيل",
  distance: "2.5 كم",
  description: "كراج متخصص في صيانة جميع أنواع السيارات مع خبرة تزيد عن 15 عاماً في المجال. نقدم خدمات صيانة شاملة بأحدث التقنيات وأفضل الأسعار.",
  services: [
    { name: "صيانة دورية", price: "200-400 ريال", duration: "2-3 ساعات" },
    { name: "إصلاح محركات", price: "500-2000 ريال", duration: "1-3 أيام" },
    { name: "كهرباء السيارات", price: "150-800 ريال", duration: "1-2 ساعات" },
    { name: "تغيير زيت", price: "80-150 ريال", duration: "30 دقيقة" },
    { name: "فحص شامل", price: "100-200 ريال", duration: "1 ساعة" },
    { name: "إطارات", price: "300-1200 ريال", duration: "1 ساعة" },
  ],
  priceRange: "متوسط",
  isOpen: true,
  openHours: "8:00 ص - 10:00 م",
  phone: "+966 11 234 5678",
  images: [
    "/api/placeholder/600/400",
    "/api/placeholder/600/400",
    "/api/placeholder/600/400",
    "/api/placeholder/600/400",
  ],
  specialization: "جميع أنواع السيارات",
  workTime: "24 ساعة",
  features: [
    "خدمة على مدار الساعة",
    "قطع غيار أصلية",
    "ضمان على جميع الأعمال",
    "خدمة التوصيل",
    "موقف مجاني",
    "استراحة للعملاء"
  ],
  contact: {
    whatsapp: "+966 50 123 4567",
    email: "<EMAIL>",
    website: "www.gulf-garage.com"
  }
};

const reviews = [
  {
    id: 1,
    user: "محمد أحمد",
    avatar: "/api/placeholder/40/40",
    rating: 5,
    comment: "خدمة ممتازة وأسعار معقولة. تم إصلاح سيارتي في الوقت المحدد بجودة عالية.",
    date: "2024-11-10",
    helpful: 12,
    images: ["/api/placeholder/100/100", "/api/placeholder/100/100"]
  },
  {
    id: 2,
    user: "فاطمة علي",
    avatar: "/api/placeholder/40/40",
    rating: 4,
    comment: "كراج نظيف وفريق عمل محترف. الوقت المستغرق أطول قليلاً من المتوقع ولكن النتيجة مرضية.",
    date: "2024-11-08",
    helpful: 8,
    images: []
  },
  {
    id: 3,
    user: "عبدالله محمد",
    avatar: "/api/placeholder/40/40",
    rating: 5,
    comment: "أفضل كراج في المنطقة. خدمة سريعة وأسعار تنافسية. أنصح به بشدة.",
    date: "2024-11-05",
    helpful: 15,
    images: ["/api/placeholder/100/100"]
  },
];

export default function GarageDetails() {
  const router = useRouter();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [userRating, setUserRating] = useState(0);
  const [userComment, setUserComment] = useState("");
  const [isLiked, setIsLiked] = useState(false);
  const { toast } = useToast();

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % garageData.images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? garageData.images.length - 1 : prev - 1
    );
  };

  const handleRatingClick = (rating: number) => {
    setUserRating(rating);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Back Button */}
      <div className="border-b border-border/50 p-4">
        <Button 
          variant="ghost" 
          onClick={() => router.back()}
          className="gap-2 font-arabic"
        >
          <ArrowRight className="w-4 h-4" />
          العودة للكراجات
        </Button>
      </div>

      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Image Gallery */}
          <div className="fade-in-up">
            <div className="relative overflow-hidden rounded-xl">
              <Image
                src={garageData.images[currentImageIndex]}
                alt={garageData.name}
                width={600}
                height={400}
                quality={100}
                className="w-full h-80 object-cover"
              />
              
              {/* Navigation Arrows */}
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
                onClick={prevImage}
              >
                <ChevronRight className="w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
                onClick={nextImage}
              >
                <ChevronLeft className="w-5 h-5" />
              </Button>

              {/* Image Indicators */}
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
                {garageData.images.map((_, index) => (
                  <button
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentImageIndex ? "bg-white" : "bg-white/50"
                    }`}
                    onClick={() => setCurrentImageIndex(index)}
                  />
                ))}
              </div>
            </div>

            {/* Thumbnail Gallery */}
            <div className="grid grid-cols-4 gap-2 mt-4">
              {garageData.images.map((image, index) => (
                <button
                  key={index}
                  className={`relative overflow-hidden rounded-lg ${
                    index === currentImageIndex ? "ring-2 ring-primary" : ""
                  }`}
                  onClick={() => setCurrentImageIndex(index)}
                >
                  <Image
                    src={image}
                    alt={`صورة ${index + 1}`}
                    width={100}
                    height={100}
                    quality={100}
                    className="w-full h-20 object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Garage Info */}
          <div className="fade-in-up" style={{ animationDelay: "0.1s" }}>
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-3xl font-almarai font-bold text-foreground">
                    {garageData.name}
                  </h1>
                  <Badge className={garageData.isOpen 
                    ? "bg-green-500 text-white" 
                    : "bg-red-500 text-white"
                  }>
                    {garageData.isOpen ? "مفتوح" : "مغلق"}
                  </Badge>
                </div>
                <p className="text-muted-foreground font-arabic mb-4">
                  {garageData.specialization}
                </p>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setIsLiked(!isLiked)}
                  className={isLiked ? "text-red-500 border-red-500" : ""}
                >
                  <Heart className={`w-5 h-5 ${isLiked ? "fill-current" : ""}`} />
                </Button>
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={() => toast({
                    title: "مشاركة الكراج",
                    description: "تم نسخ رابط الكراج",
                  })}
                >
                  <Share2 className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-4 mb-6">
              <div className="flex items-center gap-2 bg-yellow-100 dark:bg-yellow-900/30 px-3 py-1 rounded-lg">
                <Star className="w-5 h-5 text-yellow-500 fill-current" />
                <span className="font-bold text-yellow-700 dark:text-yellow-400">
                  {garageData.rating}
                </span>
              </div>
              <span className="text-muted-foreground font-arabic">
                {garageData.reviews} تقييم
              </span>
            </div>

            {/* Location */}
            <div className="flex items-center gap-3 mb-4">
              <MapPin className="w-5 h-5 text-muted-foreground" />
              <span className="font-arabic">{garageData.location}</span>
              <Badge variant="outline">{garageData.distance}</Badge>
            </div>

            {/* Description */}
            <p className="text-muted-foreground font-arabic leading-relaxed mb-6">
              {garageData.description}
            </p>

            {/* Contact Info */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-muted-foreground" />
                <span className="font-arabic">{garageData.openHours}</span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 text-muted-foreground" />
                <span>{garageData.phone}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <BookingModal garageName={garageData.name}>
                <Button className="flex-1 btn-premium text-white font-arabic gap-2">
                  <Calendar className="w-5 h-5" />
                  حجز موعد
                </Button>
              </BookingModal>
              <Button 
                variant="outline" 
                className="font-arabic gap-2"
                onClick={() => window.open(`tel:${garageData.phone}`)}
              >
                <Phone className="w-5 h-5" />
                اتصال
              </Button>
              <Button 
                variant="outline" 
                className="font-arabic gap-2"
                onClick={() => window.open(`https://wa.me/${garageData.contact.whatsapp.replace(/\D/g, '')}`)}
              >
                <MessageCircle className="w-5 h-5" />
                واتساب
              </Button>
            </div>
          </div>
        </div>

        {/* Services */}
        <Card className="premium-card p-6 fade-in-up" style={{ animationDelay: "0.2s" }}>
          <h2 className="text-2xl font-almarai font-bold text-card-foreground mb-6">
            الخدمات المتاحة
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {garageData.services.map((service, index) => (
              <div 
                key={index}
                className="border border-border rounded-lg p-4 hover:bg-muted/30 transition-colors"
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-arabic font-medium text-card-foreground">
                    {service.name}
                  </h3>
                  <Wrench className="w-5 h-5 text-primary" />
                </div>
                <div className="space-y-1 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4" />
                    <span>{service.price}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    <span>{service.duration}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Features */}
        <Card className="premium-card p-6 fade-in-up" style={{ animationDelay: "0.3s" }}>
          <h2 className="text-2xl font-almarai font-bold text-card-foreground mb-6">
            المميزات
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {garageData.features.map((feature, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="w-2 h-2 bg-primary rounded-full" />
                <span className="font-arabic text-muted-foreground">{feature}</span>
              </div>
            ))}
          </div>
        </Card>

        {/* Reviews Section */}
        <Card className="premium-card p-6 fade-in-up" style={{ animationDelay: "0.4s" }}>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-almarai font-bold text-card-foreground">
              التقييمات والمراجعات
            </h2>
            <Button 
              variant="outline" 
              className="font-arabic"
              onClick={() => toast({
                title: "عرض جميع التقييمات",
                description: "سيتم فتح صفحة التقييمات الكاملة",
              })}
            >
              عرض الكل
            </Button>
          </div>

          {/* Add Review */}
          <div className="border border-border rounded-lg p-4 mb-6">
            <h3 className="font-arabic font-medium text-card-foreground mb-4">
              إضافة تقييم
            </h3>
            
            {/* Star Rating */}
            <div className="flex items-center gap-2 mb-4">
              <span className="text-sm text-muted-foreground font-arabic">تقييمك:</span>
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => handleRatingClick(star)}
                    className="p-1"
                  >
                    <Star 
                      className={`w-5 h-5 ${
                        star <= userRating 
                          ? "text-yellow-500 fill-current" 
                          : "text-gray-300"
                      }`} 
                    />
                  </button>
                ))}
              </div>
            </div>

            <Textarea
              placeholder="اكتب تقييمك هنا..."
              value={userComment}
              onChange={(e) => setUserComment(e.target.value)}
              className="font-arabic mb-4"
              rows={3}
            />

            <div className="flex gap-3">
              <Button 
                className="btn-premium text-white font-arabic"
                onClick={() => toast({
                  title: "تم إرسال التقييم بنجاح",
                  description: "شكراً لك على تقييمك، سيساعد ذلك العملاء الآخرين",
                })}
              >
                إرسال التقييم
              </Button>
              <PhotoUploadModal>
                <Button 
                  variant="outline" 
                  className="font-arabic gap-2"
                >
                  <Camera className="w-4 h-4" />
                  إضافة صور
                </Button>
              </PhotoUploadModal>
            </div>
          </div>

          {/* Reviews List */}
          <div className="space-y-6">
            {reviews.map((review) => (
              <div key={review.id} className="border-b border-border/50 pb-6 last:border-b-0">
                <div className="flex items-start gap-4">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={review.avatar} />
                    <AvatarFallback>{review.user[0]}</AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="font-arabic font-medium text-card-foreground">
                          {review.user}
                        </h4>
                        <div className="flex items-center gap-2">
                          <div className="flex">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star 
                                key={star}
                                className={`w-4 h-4 ${
                                  star <= review.rating 
                                    ? "text-yellow-500 fill-current" 
                                    : "text-gray-300"
                                }`} 
                              />
                            ))}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {new Date(review.date).toLocaleDateString('ar-SA')}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-muted-foreground font-arabic mb-3">
                      {review.comment}
                    </p>

                    {/* Review Images */}
                    {review.images.length > 0 && (
                      <div className="flex gap-2 mb-3">
                        {review.images.map((image, index) => (
                          <Image
                            key={index}
                            src={image}
                            alt={`صورة المراجعة ${index + 1}`}
                            width={80}
                            height={80}
                            quality={100}
                            className="w-20 h-20 object-cover rounded-lg cursor-pointer"
                          />
                        ))}
                      </div>
                    )}

                    {/* Review Actions */}
                    <div className="flex items-center gap-4 text-sm">
                      <button 
                        className="flex items-center gap-1 text-muted-foreground hover:text-foreground"
                        onClick={() => toast({
                          title: "شكراً لك",
                          description: "تم تسجيل تقييمك للمراجعة",
                        })}
                      >
                        <ThumbsUp className="w-4 h-4" />
                        <span>مفيد ({review.helpful})</span>
                      </button>
                      <button 
                        className="flex items-center gap-1 text-muted-foreground hover:text-foreground"
                        onClick={() => toast({
                          title: "شكراً لك",
                          description: "تم تسجيل تقييمك للمراجعة",
                        })}
                      >
                        <ThumbsDown className="w-4 h-4" />
                        <span>غير مفيد</span>
                      </button>
                      <button 
                        className="text-muted-foreground hover:text-foreground"
                        onClick={() => toast({
                          title: "الرد على المراجعة",
                          description: "سيتم فتح نموذج الرد قريباً",
                        })}
                      >
                        رد
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}