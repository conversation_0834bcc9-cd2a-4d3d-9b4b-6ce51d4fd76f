import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Download, FileSpreadsheet, BarChart3, Pie<PERSON><PERSON>, TrendingUp } from "lucide-react";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { useToast } from "@/hooks/use-toast";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  <PERSON>,
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>,
  Pie,
  Cell,
  LineChart,
  Line,
  ResponsiveContainer
} from "recharts";

const monthlyData = [
  { month: 'يناير', revenue: 15000, orders: 45 },
  { month: 'فبراير', revenue: 18000, orders: 52 },
  { month: 'مارس', revenue: 22000, orders: 61 },
  { month: 'أبريل', revenue: 19000, orders: 48 },
  { month: 'مايو', revenue: 25000, orders: 68 },
  { month: 'يونيو', revenue: 28000, orders: 75 },
];

const servicesData = [
  { name: 'تغيير زيت', value: 35, color: '#8884d8' },
  { name: 'إصلاح مكابح', value: 25, color: '#82ca9d' },
  { name: 'فحص شامل', value: 20, color: '#ffc658' },
  { name: 'إصلاح تكييف', value: 15, color: '#ff7300' },
  { name: 'أخرى', value: 5, color: '#d084d0' },
];

const ratingsData = [
  { period: 'الأسبوع 1', rating: 4.2 },
  { period: 'الأسبوع 2', rating: 4.5 },
  { period: 'الأسبوع 3', rating: 4.8 },
  { period: 'الأسبوع 4', rating: 4.6 },
];

const Reports = () => {
  const [timeRange, setTimeRange] = useState("monthly");
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const { toast } = useToast();

  const handleExportPDF = () => {
    toast({
      title: "تصدير PDF",
      description: "تم تصدير التقرير كملف PDF بنجاح",
    });
  };

  const handleExportExcel = () => {
    toast({
      title: "تصدير Excel",
      description: "تم تصدير التقرير كملف Excel بنجاح",
    });
  };

  const formatCurrency = (value: number) => `${value.toLocaleString()} ريال`;

  return (
    <div className="flex-1 space-y-6 p-6" dir="rtl">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight text-foreground font-arabic">
            التقارير والإحصائيات
          </h1>
          <p className="text-muted-foreground font-arabic">
            تحليل شامل لأداء الكراج والعمليات
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleExportPDF} variant="outline" className="font-arabic">
            <Download className="h-4 w-4 ml-2" />
            تصدير PDF
          </Button>
          <Button onClick={handleExportExcel} variant="outline" className="font-arabic">
            <FileSpreadsheet className="h-4 w-4 ml-2" />
            تصدير Excel
          </Button>
        </div>
      </div>

      {/* Time Range Selector */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">فترة التقرير</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-full md:w-48 font-arabic">
                <SelectValue placeholder="اختر الفترة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily" className="font-arabic">يومي</SelectItem>
                <SelectItem value="weekly" className="font-arabic">أسبوعي</SelectItem>
                <SelectItem value="monthly" className="font-arabic">شهري</SelectItem>
                <SelectItem value="yearly" className="font-arabic">سنوي</SelectItem>
                <SelectItem value="custom" className="font-arabic">فترة مخصصة</SelectItem>
              </SelectContent>
            </Select>
            
            {timeRange === "custom" && (
              <>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="font-arabic">
                      <CalendarIcon className="h-4 w-4 ml-2" />
                      {startDate ? format(startDate, "PPP", { locale: ar }) : "تاريخ البداية"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={startDate} onSelect={setStartDate} />
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="font-arabic">
                      <CalendarIcon className="h-4 w-4 ml-2" />
                      {endDate ? format(endDate, "PPP", { locale: ar }) : "تاريخ النهاية"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={endDate} onSelect={setEndDate} />
                  </PopoverContent>
                </Popover>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-green-600">156,000</p>
              <p className="text-sm text-muted-foreground font-arabic">إجمالي الإيرادات (ريال)</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-blue-600">349</p>
              <p className="text-sm text-muted-foreground font-arabic">إجمالي الطلبات</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <PieChart className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-purple-600">4.6</p>
              <p className="text-sm text-muted-foreground font-arabic">متوسط التقييم</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <TrendingUp className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <p className="text-2xl font-bold text-orange-600">447</p>
              <p className="text-sm text-muted-foreground font-arabic">متوسط قيمة الطلب (ريال)</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Revenue Chart */}
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardHeader>
            <CardTitle className="text-foreground font-arabic">الإيرادات الشهرية</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" tick={{ fontSize: 12 }} />
                <YAxis tickFormatter={formatCurrency} tick={{ fontSize: 12 }} />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Legend />
                <Bar dataKey="revenue" fill="#8884d8" name="الإيرادات" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Orders Chart */}
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardHeader>
            <CardTitle className="text-foreground font-arabic">الطلبات الشهرية</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="orders" stroke="#82ca9d" name="عدد الطلبات" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Services Distribution */}
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardHeader>
            <CardTitle className="text-foreground font-arabic">توزيع الخدمات الأكثر طلباً</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={servicesData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {servicesData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Customer Ratings */}
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardHeader>
            <CardTitle className="text-foreground font-arabic">تطور تقييمات العملاء</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={ratingsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" tick={{ fontSize: 12 }} />
                <YAxis domain={[0, 5]} tick={{ fontSize: 12 }} />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="rating" stroke="#ffc658" name="التقييم" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Reports Table */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">تقرير تفصيلي</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right font-arabic p-2">الفترة</th>
                  <th className="text-right font-arabic p-2">عدد الطلبات</th>
                  <th className="text-right font-arabic p-2">الإيرادات</th>
                  <th className="text-right font-arabic p-2">متوسط قيمة الطلب</th>
                  <th className="text-right font-arabic p-2">التقييم</th>
                </tr>
              </thead>
              <tbody>
                {monthlyData.map((row, index) => (
                  <tr key={index} className="border-b hover:bg-muted/50">
                    <td className="p-2 font-arabic">{row.month}</td>
                    <td className="p-2 font-mono">{row.orders}</td>
                    <td className="p-2 font-mono">{formatCurrency(row.revenue)}</td>
                    <td className="p-2 font-mono">{formatCurrency(Math.round(row.revenue / row.orders))}</td>
                    <td className="p-2 font-mono">4.6</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Reports;