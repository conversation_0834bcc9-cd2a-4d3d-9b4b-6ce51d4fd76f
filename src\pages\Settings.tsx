import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  Shield, 
  Bell, 
  Settings as SettingsIcon, 
  LogOut, 
  Trash2, 
  Edit, 
  Eye, 
  EyeOff, 
  Camera,
  Save,
  Moon,
  Sun,
  Languages,
  Smartphone,
  Key,
  AlertTriangle
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useTheme } from "@/components/theme-provider";
import { toast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface SettingsSectionProps {
  icon: React.ReactNode;
  title: string;
  children: React.ReactNode;
}

function SettingsSection({ icon, title, children }: SettingsSectionProps) {
  return (
    <Card className="premium-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-xl font-almarai font-bold">
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center text-primary-foreground">
            {icon}
          </div>
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {children}
      </CardContent>
    </Card>
  );
}

interface ProfileInfoProps {
  userInfo: {
    name: string;
    email: string;
    phone: string;
    avatar?: string;
  };
  onUpdate: (info: {
    name: string;
    email: string;
    phone: string;
    avatar?: string;
  }) => void;
}

function ProfileInfo({ userInfo, onUpdate }: ProfileInfoProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(userInfo);

  const handleSave = () => {
    onUpdate(formData);
    setIsEditing(false);
    toast({
      title: "تم تحديث المعلومات",
      description: "تم حفظ معلوماتك الشخصية بنجاح"
    });
  };

  return (
    <SettingsSection icon={<User className="w-4 h-4" />} title="معلومات الحساب">
      <div className="flex flex-col md:flex-row items-center gap-6">
        {/* Profile Photo */}
        <div className="relative">
          <Avatar className="w-24 h-24 ring-4 ring-primary/20">
            <AvatarImage src={formData.avatar || "/api/placeholder/96/96"} />
            <AvatarFallback className="bg-gradient-to-br from-primary to-primary-dark text-primary-foreground text-2xl font-almarai font-bold">
              {formData.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <Button
            size="icon"
            variant="secondary"
            className="absolute -bottom-2 -left-2 w-8 h-8 rounded-full"
            onClick={() => toast({ title: "رفع الصورة", description: "سيتم إضافة هذه الميزة قريباً" })}
          >
            <Camera className="w-4 h-4" />
          </Button>
        </div>

        {/* Profile Form */}
        <div className="flex-1 w-full space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="font-arabic">الاسم الكامل</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                disabled={!isEditing}
                className="font-arabic"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone" className="font-arabic">رقم الهاتف</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                disabled={!isEditing}
                className="font-arabic"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="email" className="font-arabic">البريد الإلكتروني</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              disabled={!isEditing}
              className="font-arabic"
            />
          </div>
          
          <div className="flex gap-3">
            {!isEditing ? (
              <Button onClick={() => setIsEditing(true)} className="flex items-center gap-2">
                <Edit className="w-4 h-4" />
                تعديل المعلومات
              </Button>
            ) : (
              <>
                <Button onClick={handleSave} className="flex items-center gap-2">
                  <Save className="w-4 h-4" />
                  حفظ التغييرات
                </Button>
                <Button variant="outline" onClick={() => {
                  setIsEditing(false);
                  setFormData(userInfo);
                }}>
                  إلغاء
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </SettingsSection>
  );
}

function SecuritySettings() {
  const [showPassword, setShowPassword] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [loginNotifications, setLoginNotifications] = useState(true);

  return (
    <SettingsSection icon={<Shield className="w-4 h-4" />} title="إعدادات الأمان">
      <div className="space-y-6">
        {/* Change Password */}
        <div className="space-y-4">
          <h3 className="font-arabic font-semibold text-lg">تغيير كلمة المرور</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="كلمة المرور الحالية"
                className="font-arabic pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute left-2 top-1/2 -translate-y-1/2 w-6 h-6"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
            </div>
            <Input
              type="password"
              placeholder="كلمة المرور الجديدة"
              className="font-arabic"
            />
          </div>
          <Button className="flex items-center gap-2">
            <Key className="w-4 h-4" />
            تحديث كلمة المرور
          </Button>
        </div>

        <Separator />

        {/* Two-Factor Authentication */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="font-arabic font-semibold">المصادقة الثنائية</h3>
            <p className="text-sm text-muted-foreground font-arabic">
              إضافة طبقة حماية إضافية لحسابك
            </p>
          </div>
          <Switch
            checked={twoFactorEnabled}
            onCheckedChange={setTwoFactorEnabled}
          />
        </div>

        {/* Login Notifications */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="font-arabic font-semibold">تنبيهات تسجيل الدخول</h3>
            <p className="text-sm text-muted-foreground font-arabic">
              إشعارك عند تسجيل دخول جديد
            </p>
          </div>
          <Switch
            checked={loginNotifications}
            onCheckedChange={setLoginNotifications}
          />
        </div>

        {/* Last Login */}
        <div className="p-4 bg-muted/30 rounded-lg">
          <h4 className="font-arabic font-medium mb-2">آخر تسجيل دخول</h4>
          <p className="text-sm text-muted-foreground font-arabic">
            اليوم في 2:30 مساءً من جهاز iPhone
          </p>
        </div>
      </div>
    </SettingsSection>
  );
}

function NotificationSettings() {
  const [settings, setSettings] = useState({
    maintenance: true,
    license: true,
    payments: true,
    general: false
  });

  const handleToggle = (key: string) => {
    setSettings(prev => ({ ...prev, [key as keyof typeof prev]: !prev[key as keyof typeof prev] }));
  };

  const handleSave = () => {
    toast({
      title: "تم حفظ الإعدادات",
      description: "تم تحديث إعدادات الإشعارات بنجاح"
    });
  };

  const notificationTypes = [
    { key: 'maintenance', label: 'إشعارات الصيانة', description: 'تذكيرك بمواعيد الصيانة الدورية' },
    { key: 'license', label: 'إشعارات الترخيص', description: 'تنبيهك قبل انتهاء الترخيص' },
    { key: 'payments', label: 'إشعارات المدفوعات', description: 'تذكيرك بالدفعات المستحقة' },
    { key: 'general', label: 'الإشعارات العامة', description: 'تحديثات النظام والأخبار العامة' }
  ];

  return (
    <SettingsSection icon={<Bell className="w-4 h-4" />} title="إعدادات الإشعارات">
      <div className="space-y-4">
        {notificationTypes.map((type) => (
          <div key={type.key} className="flex items-center justify-between p-4 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors">
            <div className="space-y-1">
              <h3 className="font-arabic font-medium">{type.label}</h3>
              <p className="text-sm text-muted-foreground font-arabic">{type.description}</p>
            </div>
            <Switch
              checked={settings[type.key as keyof typeof settings]}
              onCheckedChange={() => handleToggle(type.key)}
            />
          </div>
        ))}
        
        <Button onClick={handleSave} className="w-full md:w-auto flex items-center gap-2">
          <Save className="w-4 h-4" />
          حفظ التغييرات
        </Button>
      </div>
    </SettingsSection>
  );
}

function AppSettings() {
  const { theme, setTheme } = useTheme();
  const [autoUpdates, setAutoUpdates] = useState(true);

  return (
    <SettingsSection icon={<SettingsIcon className="w-4 h-4" />} title="إعدادات التطبيق">
      <div className="space-y-6">
        {/* Language Selection */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="font-arabic font-semibold flex items-center gap-2">
              <Languages className="w-4 h-4" />
              اللغة
            </h3>
            <p className="text-sm text-muted-foreground font-arabic">
              اختر لغة التطبيق
            </p>
          </div>
          <Select defaultValue="ar">
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ar">العربية</SelectItem>
              <SelectItem value="en" disabled>English (قريباً)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Theme Selection */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="font-arabic font-semibold flex items-center gap-2">
              {theme === 'dark' ? <Moon className="w-4 h-4" /> : <Sun className="w-4 h-4" />}
              المظهر
            </h3>
            <p className="text-sm text-muted-foreground font-arabic">
              اختر بين المظهر الفاتح والداكن
            </p>
          </div>
          <Select value={theme} onValueChange={setTheme}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="light">فاتح</SelectItem>
              <SelectItem value="dark">داكن</SelectItem>
              <SelectItem value="system">تلقائي</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Auto Updates */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="font-arabic font-semibold flex items-center gap-2">
              <Smartphone className="w-4 h-4" />
              التحديثات التلقائية
            </h3>
            <p className="text-sm text-muted-foreground font-arabic">
              تحديث التطبيق تلقائياً عند توفر إصدار جديد
            </p>
          </div>
          <Switch
            checked={autoUpdates}
            onCheckedChange={setAutoUpdates}
          />
        </div>
      </div>
    </SettingsSection>
  );
}

function AccountManagement() {
  const handleLogout = () => {
    toast({
      title: "تم تسجيل الخروج",
      description: "سيتم إعادة توجيهك لصفحة تسجيل الدخول"
    });
  };

  const handleDeleteAccount = () => {
    toast({
      title: "تم حذف الحساب",
      description: "تم حذف حسابك نهائياً من النظام",
      variant: "destructive"
    });
  };

  return (
    <SettingsSection icon={<AlertTriangle className="w-4 h-4" />} title="إدارة الحساب">
      <div className="space-y-4">
        <div className="p-4 bg-muted/20 rounded-lg border border-border/50">
          <h3 className="font-arabic font-semibold text-lg mb-2">المنطقة الخطرة</h3>
          <p className="text-sm text-muted-foreground font-arabic mb-4">
            الإجراءات التالية لا يمكن التراجع عنها. يرجى التأكد قبل المتابعة.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="outline"
              onClick={handleLogout}
              className="flex items-center gap-2"
            >
              <LogOut className="w-4 h-4" />
              تسجيل الخروج
            </Button>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" className="flex items-center gap-2">
                  <Trash2 className="w-4 h-4" />
                  حذف الحساب
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle className="font-almarai text-right">
                    هل أنت متأكد من حذف الحساب؟
                  </AlertDialogTitle>
                  <AlertDialogDescription className="font-arabic text-right">
                    سيتم حذف جميع بياناتك نهائياً ولن يمكن استردادها. هذا الإجراء لا يمكن التراجع عنه.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex-row-reverse">
                  <AlertDialogCancel className="font-arabic">إلغاء</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteAccount}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90 font-arabic"
                  >
                    نعم، احذف الحساب
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </SettingsSection>
  );
}

export default function Settings() {
  const [userInfo, setUserInfo] = useState({
    name: "أحمد محمد العلي",
    email: "<EMAIL>",
    phone: "+966 50 123 4567",
    avatar: "/api/placeholder/96/96"
  });

  const handleUpdateProfile = (newInfo: {
    name: string;
    email: string;
    phone: string;
    avatar?: string;
  }) => {
    setUserInfo({
      ...newInfo,
      avatar: newInfo.avatar || "/api/placeholder/96/96"
    });
  };

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-3 mb-8">
        <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary-dark rounded-xl flex items-center justify-center">
          <SettingsIcon className="w-6 h-6 text-primary-foreground" />
        </div>
        <div>
          <h1 className="text-2xl md:text-3xl font-almarai font-bold text-foreground">
            الإعدادات
          </h1>
          <p className="text-muted-foreground font-arabic">
            إدارة حسابك وتخصيص إعدادات التطبيق
          </p>
        </div>
      </div>

      {/* Settings Sections */}
      <div className="space-y-6">
        <ProfileInfo userInfo={userInfo} onUpdate={handleUpdateProfile} />
        <SecuritySettings />
        <NotificationSettings />
        <AppSettings />
        <AccountManagement />
      </div>
    </div>
  );
}