import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  CreditCard, 
  CheckCircle,
  Clock,
  AlertCircle 
} from "lucide-react";

const activities = [
  {
    id: 1,
    type: "repair",
    title: "إتمام صيانة دورية",
    description: "تم إنجاز الصيانة الدورية لسيارة تويوتا كامري",
    time: "منذ ساعتين",
    status: "completed",
    icon: CheckCircle,
    color: "green",
  },
  {
    id: 2,
    type: "payment",
    title: "دفع فاتورة",
    description: "تم دفع فاتورة الصيانة بمبلغ 850 ريال",
    time: "منذ 4 ساعات",
    status: "completed",
    icon: CreditCard,
    color: "blue",
  },
  {
    id: 3,
    type: "appointment",
    title: "موعد قادم",
    description: "موعد فحص شامل لسيارة هونداي النترا",
    time: "غداً الساعة 10:00 ص",
    status: "pending",
    icon: Clock,
    color: "orange",
  },
  {
    id: 4,
    type: "alert",
    title: "تنبيه صيانة",
    description: "حان وقت تغيير زيت المحرك لسيارة نيسان التيما",
    time: "منذ يوم واحد",
    status: "warning",
    icon: AlertCircle,
    color: "red",
  },
];

const statusColors = {
  completed: "bg-green-500/10 text-green-600",
  pending: "bg-orange-500/10 text-orange-600",
  warning: "bg-destructive/10 text-destructive",
};

const iconColors = {
  green: "text-green-600 bg-green-500/10",
  blue: "text-primary bg-primary/10",
  orange: "text-orange-600 bg-orange-500/10",
  red: "text-destructive bg-destructive/10",
};

export function RecentActivity() {
  return (
    <Card className="premium-card p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-almarai font-bold text-card-foreground">
          النشاط الأخير
        </h2>
        <Badge variant="outline" className="text-xs">
          آخر 7 أيام
        </Badge>
      </div>

      <div className="space-y-4">
        {activities.map((activity, index) => (
          <div
            key={activity.id}
            className="flex items-start gap-4 p-3 rounded-lg hover:bg-muted/30 transition-colors duration-200"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className={`
              w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0
              ${iconColors[activity.color as keyof typeof iconColors]}
            `}>
              <activity.icon className="w-5 h-5" />
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2 mb-1">
                <h3 className="font-arabic font-medium text-card-foreground text-sm">
                  {activity.title}
                </h3>
                <Badge 
                  className={`text-xs shrink-0 ${statusColors[activity.status as keyof typeof statusColors]}`}
                  variant="secondary"
                >
                  {activity.status === "completed" ? "مكتمل" : 
                   activity.status === "pending" ? "قادم" : "تنبيه"}
                </Badge>
              </div>
              
              <p className="text-sm text-muted-foreground mb-2 leading-relaxed">
                {activity.description}
              </p>
              
              <span className="text-xs text-muted-foreground">
                {activity.time}
              </span>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-border/50">
        <button className="w-full text-center text-sm text-primary hover:text-primary-dark font-medium transition-colors">
          عرض جميع الأنشطة
        </button>
      </div>
    </Card>
  );
}