"use client";

import { useState } from "react";
import { Download, Share2, Filter, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>p, Car, CreditCard, Clock, CheckCircle, AlertCircle, Activity, ArrowUpRight, ArrowDownRight } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-picker";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { XAxis, YAxis, CartesianGrid, ResponsiveContainer, Pie<PERSON><PERSON> as RechartsPieChart, Cell, Pie, Area, AreaChart } from "recharts";

// Mock data
const summaryData = {
  totalVehicles: 45,
  totalPayments: 125000,
  outstandingBalance: 25000,
  completedFiles: 38,
  postponedFiles: 7
};

const monthlyPayments = [
  { month: "يناير", amount: 15000, files: 8 },
  { month: "فبراير", amount: 18000, files: 12 },
  { month: "مارس", amount: 22000, files: 15 },
  { month: "أبريل", amount: 20000, files: 11 },
  { month: "مايو", amount: 25000, files: 18 },
  { month: "يونيو", amount: 25000, files: 16 }
];

const fileStatus = [
  { name: "مكتمل", value: 38, color: "hsl(var(--success))", percentage: 63.3 },
  { name: "قيد المعالجة", value: 12, color: "hsl(var(--primary))", percentage: 20.0 },
  { name: "مؤجل", value: 7, color: "hsl(var(--warning))", percentage: 11.7 },
  { name: "ملغي", value: 3, color: "hsl(var(--destructive))", percentage: 5.0 }
];

const serviceUsage = [
  { service: "صيانة دورية", count: 25 },
  { service: "إصلاح المحرك", count: 18 },
  { service: "تغيير الإطارات", count: 15 },
  { service: "فحص شامل", count: 12 },
  { service: "إصلاح الفرامل", count: 10 }
];

const chartConfig = {
  amount: {
    label: "المبلغ (ريال)",
    color: "hsl(var(--primary))",
  },
  files: {
    label: "عدد الملفات",
    color: "hsl(var(--accent))",
  },
  growth: {
    label: "النمو",
    color: "hsl(var(--success))",
  }
};

export default function Reports() {
  const [dateRange, setDateRange] = useState<{ startDate: Date; endDate: Date } | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedVehicle, setSelectedVehicle] = useState<string>("all");

  const StatCard = ({ title, value, icon: Icon, trend, description, isPositive = true }: { title: string; value: number; icon: React.ElementType; trend: string; description: string; isPositive: boolean }) => (
    <Card className="premium-card group relative overflow-hidden transition-all duration-500 hover:scale-[1.02] border-0">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-50 group-hover:opacity-70 transition-opacity duration-500" />
      <CardHeader className="relative pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-primary/20 via-primary/10 to-transparent border border-primary/20 flex items-center justify-center backdrop-blur-sm">
              <Icon className="w-6 h-6 text-primary" />
            </div>
            <div>
              <CardTitle className="text-sm font-arabic font-medium text-muted-foreground leading-tight">
                {title}
              </CardTitle>
            </div>
          </div>
          {trend && (
            <Badge 
              variant="secondary" 
              className={`gap-1 px-2 py-1 font-medium ${
                isPositive 
                  ? 'bg-success/10 text-success border-success/20' 
                  : 'bg-destructive/10 text-destructive border-destructive/20'
              }`}
            >
              {isPositive ? <ArrowUpRight className="w-3 h-3" /> : <ArrowDownRight className="w-3 h-3" />}
              {trend}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="relative">
        <div className="space-y-2">
          <div className="text-3xl md:text-4xl font-bold text-foreground font-almarai">
            {value.toLocaleString()}
          </div>
          {description && (
            <p className="text-sm text-muted-foreground font-arabic leading-relaxed">{description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="flex-1 space-y-6 p-4 md:p-6 lg:p-8 min-h-screen bg-gradient-to-br from-background via-background to-muted/5">
      {/* Header */}
      <div className="space-y-3 fade-in-up">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center">
            <BarChart3 className="w-5 h-5 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-foreground font-almarai heading-arabic">
              التقارير والإحصائيات
            </h1>
            <p className="text-muted-foreground font-arabic text-sm md:text-base">
              تتبع أداء خدمات السيارات والإحصائيات المالية بشكل تفاعلي
            </p>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 md:gap-6 scale-in">
        <StatCard
          title="عدد المركبات"
          value={summaryData.totalVehicles}
          icon={Car}
          trend="+12%"
          isPositive={true}
          description="إجمالي المركبات المسجلة"
        />
        <StatCard
          title="إجمالي المدفوعات"
          value={summaryData.totalPayments}
          icon={CreditCard}
          trend="+8%"
          isPositive={true}
          description="ريال سعودي"
        />
        <StatCard
          title="الرصيد المتبقي"
          value={summaryData.outstandingBalance}
          icon={Clock}
          trend="-5%"
          isPositive={false}
          description="مستحقات معلقة"
        />
        <StatCard
          title="الملفات المكتملة"
          value={summaryData.completedFiles}
          icon={CheckCircle}
          trend="+15%"
          isPositive={true}
          description="تم إنجازها بنجاح"
        />
        <StatCard
          title="الملفات المؤجلة"
          value={summaryData.postponedFiles}
          icon={AlertCircle}
          trend="-3%"
          isPositive={false}
          description="تحتاج متابعة"
        />
      </div>

      {/* Filters */}
      <Card className="premium-card glass-effect slide-in-right">
        <CardHeader className="pb-4">
          <CardTitle className="font-almarai flex items-center gap-3 text-lg">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
              <Filter className="w-4 h-4 text-primary" />
            </div>
            أدوات التصفية والتحليل المتقدم
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 md:gap-6">
            <div className="space-y-3">
              <label className="text-sm font-arabic font-semibold text-foreground">الفترة الزمنية</label>
              <DatePickerWithRange 
                value={dateRange}
                onChange={setDateRange}
                placeholder="من - إلى"
                className="w-full"
              />
            </div>
            <div className="space-y-3">
              <label className="text-sm font-arabic font-semibold text-foreground">حالة الملف</label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="input-premium">
                  <SelectValue placeholder="اختر الحالة" />
                </SelectTrigger>
                <SelectContent className="border-border/50">
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="completed">مكتمل</SelectItem>
                  <SelectItem value="pending">قيد المعالجة</SelectItem>
                  <SelectItem value="postponed">مؤجل</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-3">
              <label className="text-sm font-arabic font-semibold text-foreground">المركبة</label>
              <Select value={selectedVehicle} onValueChange={setSelectedVehicle}>
                <SelectTrigger className="input-premium">
                  <SelectValue placeholder="اختر المركبة" />
                </SelectTrigger>
                <SelectContent className="border-border/50">
                  <SelectItem value="all">جميع المركبات</SelectItem>
                  <SelectItem value="camry">كامري 2020</SelectItem>
                  <SelectItem value="accord">أكورد 2019</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-3">
              <label className="text-sm font-arabic font-semibold text-foreground">الإجراءات</label>
              <Button className="btn-premium w-full font-arabic h-12 rounded-xl">
                <Activity className="w-4 h-4 ml-2" />
                تحديث التقرير
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts Section */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Monthly Payments Chart */}
        <Card className="premium-card group overflow-hidden">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="font-almarai flex items-center gap-3 text-lg">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                  <BarChart3 className="w-4 h-4 text-primary" />
                </div>
                تطور المدفوعات الشهرية
              </CardTitle>
              <Badge variant="outline" className="gap-1 border-primary/20 text-primary">
                <TrendingUp className="w-3 h-3" />
                +18% هذا الشهر
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-[350px] md:h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={monthlyPayments} margin={{ top: 20, right: 20, left: 20, bottom: 20 }}>
                  <defs>
                    <linearGradient id="amountGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.3} />
                      <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0.05} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" strokeOpacity={0.3} />
                  <XAxis 
                    dataKey="month" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                    tickFormatter={(value) => `${value.toLocaleString()} ر.س`}
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Area 
                    type="monotone" 
                    dataKey="amount" 
                    stroke="hsl(var(--primary))" 
                    strokeWidth={3}
                    fill="url(#amountGradient)"
                    dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: 'hsl(var(--primary))', strokeWidth: 2, fill: 'hsl(var(--background))' }}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* File Status Distribution */}
        <Card className="premium-card group overflow-hidden">
          <CardHeader className="pb-4">
            <CardTitle className="font-almarai flex items-center gap-3 text-lg">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-accent/20 to-accent/10 flex items-center justify-center">
                <PieChart className="w-4 h-4 text-accent" />
              </div>
              توزيع حالة الملفات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center mb-6">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie 
                    data={fileStatus} 
                    cx="50%" 
                    cy="50%" 
                    innerRadius={60}
                    outerRadius={120} 
                    dataKey="value"
                    strokeWidth={2}
                    stroke="hsl(var(--background))"
                  >
                    {fileStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <ChartTooltip 
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="premium-card p-3 border border-border/50">
                            <div className="flex items-center gap-2 mb-2">
                              <div 
                                className="w-3 h-3 rounded-full" 
                                style={{ backgroundColor: data.color }}
                              />
                              <span className="font-arabic font-semibold">{data.name}</span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              العدد: {data.value} ملف ({data.percentage}%)
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-2 gap-3">
              {fileStatus.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-xl bg-muted/30 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-sm font-arabic font-medium">{item.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold">{item.value}</div>
                    <div className="text-xs text-muted-foreground">{item.percentage}%</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Service Usage */}
      <Card className="premium-card">
        <CardHeader className="pb-4">
          <CardTitle className="font-almarai flex items-center justify-between text-lg">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-success/20 to-success/10 flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-success" />
              </div>
              الخدمات الأكثر استخداماً
            </div>
            <Badge variant="outline" className="gap-1 border-success/20 text-success">
              <Activity className="w-3 h-3" />
              آخر 6 أشهر
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {serviceUsage.map((service, index) => (
              <div key={index} className="group flex items-center justify-between p-4 rounded-xl bg-gradient-to-r from-muted/30 to-muted/10 hover:from-muted/50 hover:to-muted/20 transition-all duration-300 border border-border/30">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                    <span className="text-sm font-bold text-primary">{index + 1}</span>
                  </div>
                  <span className="font-arabic font-semibold text-foreground">{service.service}</span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-32 md:w-40 bg-muted rounded-full h-3 overflow-hidden">
                    <div 
                      className="bg-gradient-to-r from-primary via-primary-light to-primary h-3 rounded-full transition-all duration-700 ease-out group-hover:shadow-md"
                      style={{ 
                        width: `${(service.count / 25) * 100}%`,
                        boxShadow: '0 0 10px hsl(var(--primary) / 0.3)'
                      }}
                    />
                  </div>
                  <Badge variant="secondary" className="min-w-[60px] justify-center bg-primary/10 text-primary border-primary/20 font-bold">
                    {service.count}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Export Section */}
      <Card className="premium-card relative overflow-hidden border-0">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-accent/5 to-primary/5" />
        <CardContent className="relative pt-6 pb-6">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div className="space-y-3 text-center md:text-right">
              <div className="flex items-center gap-3 justify-center md:justify-start">
                <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center">
                  <Download className="w-5 h-5 text-primary-foreground" />
                </div>
                <h3 className="text-xl font-bold font-almarai text-foreground">تصدير ومشاركة التقرير</h3>
              </div>
              <p className="text-muted-foreground font-arabic max-w-md">
                احصل على نسخة PDF مفصلة من التقرير أو شاركه مباشرة مع فريق العمل والعملاء
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
              <Button 
                variant="outline" 
                className="font-arabic h-12 px-6 rounded-xl border-primary/20 hover:bg-primary/5 hover:border-primary/30 transition-all duration-300"
              >
                <Share2 className="w-4 h-4 ml-2" />
                مشاركة التقرير
              </Button>
              <Button className="btn-premium font-arabic h-12 px-6 rounded-xl">
                <Download className="w-4 h-4 ml-2" />
                تحميل PDF
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}