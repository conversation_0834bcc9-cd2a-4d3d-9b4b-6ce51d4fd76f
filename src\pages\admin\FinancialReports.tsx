import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from "recharts";
import {
  DollarSign,
  TrendingUp,
  Download,
  Building2,
  Percent,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Mock financial data
const revenueData = [
  { month: "يناير", daily: 15000, monthly: 450000, yearly: 5400000, commission: 45000, subscriptions: 12000 },
  { month: "فبراير", daily: 18000, monthly: 540000, yearly: 6480000, commission: 54000, subscriptions: 15000 },
  { month: "مارس", daily: 16500, monthly: 495000, yearly: 5940000, commission: 49500, subscriptions: 13500 },
  { month: "أبريل", daily: 22000, monthly: 660000, yearly: 7920000, commission: 66000, subscriptions: 18000 },
  { month: "مايو", daily: 24500, monthly: 735000, yearly: 8820000, commission: 73500, subscriptions: 20000 },
  { month: "يونيو", daily: 28000, monthly: 840000, yearly: 10080000, commission: 84000, subscriptions: 24000 },
];

const commissionData = [
  { service: "تغيير الزيت", orders: 540, commission: 16200, percentage: 3 },
  { service: "فحص شامل", orders: 420, commission: 25200, percentage: 6 },
  { service: "إصلاح الفرامل", orders: 280, commission: 22400, percentage: 8 },
  { service: "صيانة التكييف", orders: 180, commission: 18000, percentage: 10 },
  { service: "أخرى", orders: 320, commission: 19200, percentage: 6 },
];

const subscriptionData = [
  { type: "مجاني", count: 850, revenue: 0, color: "#94a3b8" },
  { type: "مميز", count: 320, revenue: 96000, color: "#3b82f6" },
  { type: "مؤسسي", count: 45, revenue: 67500, color: "#8b5cf6" },
];

const adRevenue = [
  { month: "يناير", bannerAds: 8000, promotedListings: 12000, featured: 5000 },
  { month: "فبراير", bannerAds: 9500, promotedListings: 14000, featured: 6200 },
  { month: "مارس", bannerAds: 8800, promotedListings: 13200, featured: 5800 },
  { month: "أبريل", bannerAds: 11000, promotedListings: 16000, featured: 7500 },
  { month: "مايو", bannerAds: 12500, promotedListings: 18000, featured: 8200 },
  { month: "يونيو", bannerAds: 14000, promotedListings: 20000, featured: 9500 },
];

export default function FinancialReports() {
  const [timeRange, setTimeRange] = useState("monthly");
  const { toast } = useToast();

  const handleExport = (format: string) => {
    toast({
      title: "تم تصدير التقرير",
      description: `تم تصدير التقرير المالي بصيغة ${format}`,
    });
  };

  const totalRevenue = revenueData.reduce((sum, item) => sum + item.monthly, 0);
  const totalCommission = revenueData.reduce((sum, item) => sum + item.commission, 0);
  const totalSubscriptions = revenueData.reduce((sum, item) => sum + item.subscriptions, 0);
  const totalAdRevenue = adRevenue.reduce((sum, item) => sum + item.bannerAds + item.promotedListings + item.featured, 0);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground font-arabic">التقارير المالية</h1>
          <p className="text-muted-foreground font-arabic">تحليل شامل للإيرادات والأرباح</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">يومي</SelectItem>
              <SelectItem value="monthly">شهري</SelectItem>
              <SelectItem value="yearly">سنوي</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={() => handleExport("PDF")}>
            <Download className="w-4 h-4 ml-2" />
            PDF
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExport("Excel")}>
            <Download className="w-4 h-4 ml-2" />
            Excel
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 border-green-200 dark:border-green-800/30">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-700 dark:text-green-300 font-arabic">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-green-800 dark:text-green-200">
                  {totalRevenue.toLocaleString()} ر.س
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200 dark:border-blue-800/30">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-700 dark:text-blue-300 font-arabic">عمولة المنصة</p>
                <p className="text-2xl font-bold text-blue-800 dark:text-blue-200">
                  {totalCommission.toLocaleString()} ر.س
                </p>
              </div>
              <Percent className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-900/20 dark:to-purple-800/10 border-purple-200 dark:border-purple-800/30">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-700 dark:text-purple-300 font-arabic">اشتراكات الكراجات</p>
                <p className="text-2xl font-bold text-purple-800 dark:text-purple-200">
                  {totalSubscriptions.toLocaleString()} ر.س
                </p>
              </div>
              <Building2 className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100/50 dark:from-orange-900/20 dark:to-orange-800/10 border-orange-200 dark:border-orange-800/30">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-orange-700 dark:text-orange-300 font-arabic">إيرادات الإعلانات</p>
                <p className="text-2xl font-bold text-orange-800 dark:text-orange-200">
                  {totalAdRevenue.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="revenue" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="revenue" className="font-arabic">الإيرادات</TabsTrigger>
          <TabsTrigger value="commission" className="font-arabic">العمولات</TabsTrigger>
          <TabsTrigger value="subscriptions" className="font-arabic">الاشتراكات</TabsTrigger>
          <TabsTrigger value="advertising" className="font-arabic">الإعلانات</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="font-arabic">نمو الإيرادات الشهرية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis dataKey="month" className="text-xs" />
                      <YAxis className="text-xs" />
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: 'hsl(var(--card))',
                          border: '1px solid hsl(var(--border))',
                          borderRadius: '8px'
                        }}
                      />
                      <Legend />
                      <Area 
                        type="monotone" 
                        dataKey="monthly" 
                        name="الإيرادات الشهرية"
                        stroke="#10b981" 
                        fill="#10b981" 
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="font-arabic">مقارنة الإيرادات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis dataKey="month" className="text-xs" />
                      <YAxis className="text-xs" />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="commission" name="العمولات" fill="#3b82f6" />
                      <Bar dataKey="subscriptions" name="الاشتراكات" fill="#8b5cf6" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="commission" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="font-arabic">تفصيل عمولات الخدمات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {commissionData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-4 rounded-lg bg-muted/30">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-bold text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium font-arabic">{item.service}</p>
                        <p className="text-sm text-muted-foreground">{item.orders} طلب</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">{item.commission.toLocaleString()} ر.س</p>
                      <Badge variant="secondary">{item.percentage}% عمولة</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="font-arabic">توزيع الاشتراكات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={subscriptionData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        paddingAngle={2}
                        dataKey="count"
                      >
                        {subscriptionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="font-arabic">إيرادات الاشتراكات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {subscriptionData.map((sub, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 rounded-full" style={{ backgroundColor: sub.color }}></div>
                        <span className="font-arabic">{sub.type}</span>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{sub.revenue.toLocaleString()} ر.س</p>
                        <p className="text-sm text-muted-foreground">{sub.count} مشترك</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="advertising" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="font-arabic">إيرادات الإعلانات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={adRevenue}>
                    <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                    <XAxis dataKey="month" className="text-xs" />
                    <YAxis className="text-xs" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="bannerAds" name="الإعلانات البانر" fill="#f59e0b" />
                    <Bar dataKey="promotedListings" name="القوائم المروجة" fill="#ef4444" />
                    <Bar dataKey="featured" name="المميزة" fill="#10b981" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}