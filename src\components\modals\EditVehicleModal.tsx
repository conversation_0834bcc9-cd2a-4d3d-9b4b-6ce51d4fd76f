import { useState } from "react";
import { Edit } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";

interface EditVehicleModalProps {
  children: React.ReactNode;
  vehicle?: {
    name: string;
    model: string;
    chassisNumber: string;
    licensePlate: string;
    color: string;
    mileage: string;
    licenseExpiry: string;
    notes?: string;
  };
}

export function EditVehicleModal({ children, vehicle }: EditVehicleModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: vehicle?.name || "",
    model: vehicle?.model || "",
    chassisNumber: vehicle?.chassisNumber || "",
    licensePlate: vehicle?.licensePlate || "",
    color: vehicle?.color || "",
    mileage: vehicle?.mileage || "",
    licenseExpiry: vehicle?.licenseExpiry || "",
    notes: vehicle?.notes || ""
  });
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "تم تحديث المركبة بنجاح",
      description: `تم تحديث بيانات ${formData.name}`,
    });
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-almarai flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
              <Edit className="w-4 h-4 text-primary-foreground" />
            </div>
            تعديل بيانات المركبة
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Card className="premium-card p-4">
            <h3 className="font-arabic font-semibold mb-4">معلومات أساسية</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-name" className="font-arabic">اسم المركبة</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="font-arabic"
                />
              </div>
              <div>
                <Label htmlFor="edit-model" className="font-arabic">الموديل</Label>
                <Input
                  id="edit-model"
                  value={formData.model}
                  onChange={(e) => setFormData({...formData, model: e.target.value})}
                  className="font-arabic"
                />
              </div>
              <div>
                <Label htmlFor="edit-chassis" className="font-arabic">رقم الشاصي</Label>
                <Input
                  id="edit-chassis"
                  value={formData.chassisNumber}
                  onChange={(e) => setFormData({...formData, chassisNumber: e.target.value})}
                  className="font-mono"
                />
              </div>
              <div>
                <Label htmlFor="edit-plate" className="font-arabic">رقم اللوحة</Label>
                <Input
                  id="edit-plate"
                  value={formData.licensePlate}
                  onChange={(e) => setFormData({...formData, licensePlate: e.target.value})}
                  className="font-arabic"
                />
              </div>
              <div>
                <Label htmlFor="edit-color" className="font-arabic">اللون</Label>
                <Input
                  id="edit-color"
                  value={formData.color}
                  onChange={(e) => setFormData({...formData, color: e.target.value})}
                  className="font-arabic"
                />
              </div>
              <div>
                <Label htmlFor="edit-mileage" className="font-arabic">المسافة المقطوعة</Label>
                <Input
                  id="edit-mileage"
                  value={formData.mileage}
                  onChange={(e) => setFormData({...formData, mileage: e.target.value})}
                  className="font-arabic"
                />
              </div>
            </div>
          </Card>

          <div className="flex gap-3 pt-4">
            <Button type="submit" className="flex-1 btn-premium text-white font-arabic gap-2">
              <Edit className="w-4 h-4" />
              حفظ التغييرات
            </Button>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} className="font-arabic">
              إلغاء
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}