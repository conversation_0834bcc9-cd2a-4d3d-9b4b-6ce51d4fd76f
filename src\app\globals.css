@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&family=Almarai:wght@300;400;700;800&display=swap');
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  /* Colors - Light Mode */
  --color-background: 240 100.0000% 98.0392%;
  --color-foreground: 240 27.5862% 22.7451%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 240 27.5862% 22.7451%;
  --color-popover: 0 0% 100%;
  --color-popover-foreground: 240 27.5862% 22.7451%;

  /* Premium Blue/Purple Primary */
  --color-primary: 251.9008 55.7604% 57.4510%;
  --color-primary-foreground: 0 0% 100%;
  --color-primary-light: 249.6429 94.9153% 76.8627%;
  --color-primary-dark: 243.6522 47.3251% 47.6471%;

  /* Premium Secondary */
  --color-secondary: 249.3750 100% 93.7255%;
  --color-secondary-foreground: 249.3750 33.3333% 37.6471%;

  /* Muted colors */
  --color-muted: 240 50.0000% 96.0784%;
  --color-muted-foreground: 240 12.1951% 48.2353%;

  /* Accent colors */
  --color-accent: 218.4615 100.0000% 92.3529%;
  --color-accent-foreground: 240 27.5862% 22.7451%;

  /* Status colors */
  --color-success: 142 71% 45%;
  --color-success-foreground: 0 0% 100%;
  --color-warning: 38 92% 50%;
  --color-warning-foreground: 0 0% 100%;
  --color-destructive: 350.1754 100% 66.4706%;
  --color-destructive-foreground: 0 0% 100%;

  /* Borders and inputs */
  --color-border: 240 34.7826% 90.9804%;
  --color-input: 240 34.7826% 90.9804%;
  --color-ring: 251.9008 55.7604% 57.4510%;

  /* Sidebar specific */
  --color-sidebar-background: 240 50.0000% 96.0784%;
  --color-sidebar-foreground: 240 27.5862% 22.7451%;
  --color-sidebar-primary: 251.9008 55.7604% 57.4510%;
  --color-sidebar-primary-foreground: 0 0% 100%;
  --color-sidebar-accent: 218.4615 100.0000% 92.3529%;
  --color-sidebar-accent-foreground: 240 27.5862% 22.7451%;
  --color-sidebar-border: 240 34.7826% 90.9804%;
  --color-sidebar-ring: 251.9008 55.7604% 57.4510%;

  /* Radius */
  --radius: 0.5rem;

  /* Spacing */
  --spacing: 0.25rem;

  /* Font families */
  --font-family-arabic: 'IBM Plex Sans Arabic', sans-serif;
  --font-family-almarai: 'Almarai', sans-serif;

  /* Premium Gradients */
  --gradient-primary: linear-gradient(135deg, hsl(251.9008 55.7604% 57.4510%) 0%, hsl(243.6522 47.3251% 47.6471%) 100%);
  --gradient-secondary: linear-gradient(135deg, hsl(249.3750 100% 93.7255%) 0%, hsl(240 50.0000% 96.0784%) 100%);
  --gradient-dark: linear-gradient(135deg, hsl(240 26.8293% 8.0392%) 0%, hsl(240 27.7778% 14.1176%) 100%);
  --gradient-glass: linear-gradient(135deg, hsla(251.9008 55.7604% 57.4510% / 0.1) 0%, hsla(243.6522 47.3251% 47.6471% / 0.05) 100%);

  /* Premium Shadows */
  --shadow-soft: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-medium: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-strong: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-glow: 0 0 0 1px hsl(251.9008 55.7604% 57.4510% / 0.1), 0 0 20px hsl(251.9008 55.7604% 57.4510% / 0.3);

  /* Animations */
  --animate-fade-in-up: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  --animate-scale-in: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  --animate-slide-in-right: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Dark Mode Colors */
.dark {
  --color-background: 240 26.8293% 8.0392%;
  --color-foreground: 240 48.7179% 92.3529%;
  --color-card: 240 27.7778% 14.1176%;
  --color-card-foreground: 240 48.7179% 92.3529%;
  --color-popover: 240 27.7778% 14.1176%;
  --color-popover-foreground: 240 48.7179% 92.3529%;

  /* Primary stays vibrant in dark */
  --color-primary: 251.2500 100% 78.0392%;
  --color-primary-foreground: 240 26.8293% 8.0392%;
  --color-primary-light: 230.4878 44.0860% 63.5294%;
  --color-primary-dark: 174.2857 41.8327% 50.7843%;

  --color-secondary: 242.8571 32.8125% 25.0980%;
  --color-secondary-foreground: 241.9672 100% 88.0392%;

  --color-muted: 240 33.3333% 20%;
  --color-muted-foreground: 240 20.2532% 69.0196%;

  --color-accent: 240 33.3333% 28.2353%;
  --color-accent-foreground: 240 48.7179% 92.3529%;

  --color-success: 142 71% 55%;
  --color-warning: 38 92% 60%;
  --color-destructive: 350.1754 100% 66.4706%;

  --color-border: 240 26.1538% 25.4902%;
  --color-input: 240 26.1538% 25.4902%;
  --color-ring: 251.2500 100% 78.0392%;

  --color-sidebar-background: 240 27.7778% 14.1176%;
  --color-sidebar-foreground: 240 48.7179% 92.3529%;
  --color-sidebar-primary: 251.2500 100% 78.0392%;
  --color-sidebar-primary-foreground: 240 26.8293% 8.0392%;
  --color-sidebar-accent: 240 33.3333% 28.2353%;
  --color-sidebar-accent-foreground: 240 48.7179% 92.3529%;
  --color-sidebar-border: 240 26.1538% 25.4902%;
  --color-sidebar-ring: 251.2500 100% 78.0392%;

  /* Dark mode gradients and shadows */
  --gradient-primary: linear-gradient(135deg, hsl(251.2500 100% 78.0392%) 0%, hsl(174.2857 41.8327% 50.7843%) 100%);
  --gradient-secondary: linear-gradient(135deg, hsl(242.8571 32.8125% 25.0980%) 0%, hsl(240 27.7778% 14.1176%) 100%);
  --gradient-dark: linear-gradient(135deg, hsl(240 26.8293% 8.0392%) 0%, hsl(240 27.7778% 14.1176%) 100%);
  --gradient-glass: linear-gradient(135deg, hsla(251.2500 100% 78.0392% / 0.15) 0%, hsla(174.2857 41.8327% 50.7843% / 0.08) 100%);

  --shadow-soft: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-medium: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-strong: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-glow: 0 0 0 1px hsl(251.2500 100% 78.0392% / 0.2), 0 0 30px hsl(251.2500 100% 78.0392% / 0.4);
}

/* System preference fallback for dark mode */
@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    --color-background: 240 26.8293% 8.0392%;
    --color-foreground: 240 48.7179% 92.3529%;
    --color-card: 240 27.7778% 14.1176%;
    --color-card-foreground: 240 48.7179% 92.3529%;
    --color-popover: 240 27.7778% 14.1176%;
    --color-popover-foreground: 240 48.7179% 92.3529%;

    --color-primary: 251.2500 100% 78.0392%;
    --color-primary-foreground: 240 26.8293% 8.0392%;
    --color-primary-light: 230.4878 44.0860% 63.5294%;
    --color-primary-dark: 174.2857 41.8327% 50.7843%;

    --color-secondary: 242.8571 32.8125% 25.0980%;
    --color-secondary-foreground: 241.9672 100% 88.0392%;

    --color-muted: 240 33.3333% 20%;
    --color-muted-foreground: 240 20.2532% 69.0196%;

    --color-accent: 240 33.3333% 28.2353%;
    --color-accent-foreground: 240 48.7179% 92.3529%;

    --color-success: 142 71% 55%;
    --color-warning: 38 92% 60%;
    --color-destructive: 350.1754 100% 66.4706%;

    --color-border: 240 26.1538% 25.4902%;
    --color-input: 240 26.1538% 25.4902%;
    --color-ring: 251.2500 100% 78.0392%;

    --color-sidebar-background: 240 27.7778% 14.1176%;
    --color-sidebar-foreground: 240 48.7179% 92.3529%;
    --color-sidebar-primary: 251.2500 100% 78.0392%;
    --color-sidebar-primary-foreground: 240 26.8293% 8.0392%;
    --color-sidebar-accent: 240 33.3333% 28.2353%;
    --color-sidebar-accent-foreground: 240 48.7179% 92.3529%;
    --color-sidebar-border: 240 26.1538% 25.4902%;
    --color-sidebar-ring: 251.2500 100% 78.0392%;

    --gradient-primary: linear-gradient(135deg, hsl(251.2500 100% 78.0392%) 0%, hsl(174.2857 41.8327% 50.7843%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(242.8571 32.8125% 25.0980%) 0%, hsl(240 27.7778% 14.1176%) 100%);
    --gradient-dark: linear-gradient(135deg, hsl(240 26.8293% 8.0392%) 0%, hsl(240 27.7778% 14.1176%) 100%);
    --gradient-glass: linear-gradient(135deg, hsla(251.2500 100% 78.0392% / 0.15) 0%, hsla(174.2857 41.8327% 50.7843% / 0.08) 100%);

    --shadow-soft: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
    --shadow-medium: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
    --shadow-strong: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
    --shadow-glow: 0 0 0 1px hsl(251.2500 100% 78.0392% / 0.2), 0 0 30px hsl(251.2500 100% 78.0392% / 0.4);
  }
}

/* RTL Support */
html {
  direction: rtl;
}

body {
  background-color: hsl(var(--color-background));
  color: hsl(var(--color-foreground));
  font-family: var(--font-family-arabic);
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
}

/* Border color for all elements */
* {
  border-color: hsl(var(--color-border));
}

/* Premium scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: hsl(var(--color-muted) / 0.3);
}

::-webkit-scrollbar-thumb {
  background-color: hsl(var(--color-primary) / 0.4);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--color-primary) / 0.6);
}

/* Component classes */
.glass-effect {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid hsl(var(--color-border) / 0.2);
}

.premium-card {
  background-color: hsl(var(--color-card));
  border: 1px solid hsl(var(--color-border) / 0.5);
  border-radius: calc(var(--radius) + 4px);
  box-shadow: var(--shadow-soft);
  background: linear-gradient(135deg, hsl(var(--color-card)) 0%, hsl(var(--color-card) / 0.95) 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.btn-premium {
  position: relative;
  overflow: hidden;
  background: var(--gradient-primary);
  box-shadow: var(--shadow-soft);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-premium:hover {
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

.input-premium {
  background-color: hsl(var(--color-card));
  border: 1px solid hsl(var(--color-border) / 0.5);
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-premium:focus {
  border-color: hsl(var(--color-primary) / 0.5);
  box-shadow: 0 0 0 2px hsl(var(--color-primary) / 0.2), var(--shadow-glow);
  outline: none;
}

.heading-arabic {
  font-family: var(--font-family-almarai);
  font-weight: 700;
  letter-spacing: 0.025em;
  text-shadow: 0 1px 2px hsl(var(--color-foreground) / 0.1);
}

.text-arabic {
  font-family: var(--font-family-arabic);
  line-height: 1.625;
}

/* Animation classes */
.fade-in-up {
  animation: var(--animate-fade-in-up);
}

.scale-in {
  animation: var(--animate-scale-in);
}

.slide-in-right {
  animation: var(--animate-slide-in-right);
}

/* Mobile responsive utilities */
@media (max-width: 768px) {
  .premium-card {
    border-radius: var(--radius);
    border: none;
    box-shadow: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -2px hsl(0 0% 0% / 0.1);
    background: linear-gradient(135deg, hsl(var(--color-card)) 0%, hsl(var(--color-card) / 0.98) 100%);
  }
  
  .glass-effect {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
  
  .mobile-app-layout {
    min-height: 100vh;
    background: linear-gradient(to bottom right, hsl(var(--color-background)), hsl(var(--color-background)), hsl(var(--color-muted) / 0.1));
  }
  
  .sidebar-mobile {
    width: 100%;
    max-width: 20rem;
    box-shadow: 0 25px 50px -12px hsl(0 0% 0% / 0.25);
  }
  
  .mobile-text-lg {
    font-size: 1.125rem;
    line-height: 1.375;
  }
  
  .mobile-text-base {
    font-size: 1rem;
    line-height: 1.375;
  }
  
  .mobile-card-spacing {
    padding: 1rem;
    gap: 1rem;
  }
  
  .mobile-btn {
    height: 3rem;
    padding: 0 1.5rem;
    border-radius: calc(var(--radius) + 4px);
    font-weight: 500;
  }
  
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
    padding-top: env(safe-area-inset-top);
  }
  
  .sidebar-mobile {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 50;
    width: 20rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }
  
  .sidebar-mobile[data-state="collapsed"] {
    transform: translateX(-100%);
  }
  
  .sidebar-mobile[data-state="expanded"] {
    transform: translateX(0);
  }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  .premium-card {
    background: linear-gradient(135deg, hsl(var(--color-card)) 0%, hsl(var(--color-card) / 0.98) 100%);
    border-color: hsl(var(--color-border) / 0.3);
  }
  
  .glass-effect {
    background: linear-gradient(135deg, hsla(217 91% 65% / 0.08) 0%, hsla(217 91% 50% / 0.04) 100%);
    border-color: hsl(var(--color-border) / 0.1);
  }
}

/* Touch improvements for mobile */
@media (hover: none) and (pointer: coarse) {
  .hover-scale {
    transform: none;
  }
  
  .premium-card:hover {
    transform: none;
  }
  
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
